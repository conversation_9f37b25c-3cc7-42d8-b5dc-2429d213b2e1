<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>导入excel文件</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">

        var gps=getQueryString();

        var dataArray = [];
        $(function () {
            // 加载表单
            loadForm("examQuestionImportTableAddForm");
            $("#buttons").dialog("close");//子选项对话框
            //模板下载
            $(".export").on("click", function () {
                // 下载模板地址
                $("#exportForm").attr("action", web.rootdir + "action/examQuestion/downloadTemplate");
                $("#exportForm .exportSubmit").trigger("click");
            });
        });



        //显示
        function getInfoList(data, $t) {
            console.log(data)
            if (data.data) {
                dataArray = data.data.listData;
                var pageparam = {
                    "listtable": {
                        "listname": "#lzTable",//table列表的id名称，需加#
                        "nowrap": true,//把数据显示在一行里,默认true
                        "fitColumns": true,
                        "styleClass":"noScroll",
                        "loadFilter": pagerFilter,
                        "data": {data:{originalRows:data.data.listData,total:0}},
                        "columns": [[//列
                            {title: "题库编码", field: "questionBankCode", width: 150,tooltip:true},
                            {title: "题目名称", field: "questionName", width: 300,tooltip:true},
                            {title: "题目类型", field: "questionType", width: 150,tooltip:true},
                            {title: "题目分数", field: "questionScore", width: 150,tooltip:true},
                            {title: "答案A", field: "answerA", width: 150,tooltip:true},
                            {title: "答案B", field: "answerB", width: 150,tooltip:true},
                            {title: "答案C", field: "answerC", width: 150,tooltip:true},
                            {title: "答案D", field: "answerD", width: 150,tooltip:true},
                            {title: "答案E", field: "answerE", width: 150,tooltip:true},
                            {title: "答案F", field: "answerF", width: 150,tooltip:true},
                            {title: "答案G", field: "answerG", width: 150,tooltip:true},
                            {title: "答案H", field: "answerH", width: 150,tooltip:true},
                            {title: "答案I", field: "answerI", width: 150,tooltip:true},
                            {title: "答案J", field: "answerJ", width: 150,tooltip:true},
                            {title: "答案K", field: "answerK", width: 150,tooltip:true},
                            {title: "答案L", field: "answerL", width: 150,tooltip:true},
                            {title: "正确答案", field: "answerIsTrue", width: 150,tooltip:true}
                        ]]
                    }

                };
                loadGrid(pageparam);

            }
            // $($("#lzTable").datagrid("getPager")).pagination({pageSize:20});

        };

        window.getchoosedata=function() {
            var data= new Object();
            data.answerListImport = dataArray;
            return {"data": data, "state": 1};
        }


    </script>
</head>
<body>
<!--scontentType获取数据时设置传参方式，不设置默认为kv  cmd-select获取数据命令配置，不配置自动从列表中取   cmd-insert新增  cmd-update修改-->
<div class="basic tab_table">
    <!--导入excel-->
    <form id="examQuestionImportTableAddForm" method="post" contentType="application/json; charset=utf-8">
        <fieldset class="title">
            <legend><font>友情提示</font><font
                    class="ml15 f12 col_r">1、不能含有公式。2、数据不能为空。3、IE8及IE9，请双击鼠标进行上传。</font></legend>
        </fieldset>
        <table border="0" cellpadding="0" cellspacing="10" width="100%">
            <tr>
                <td align="right" width="140" class="lh32">请选择文件</td>
                <td width="320" valign="top">
                    <input id="targetFilePaths" name="targetFilePaths" type="text" mesTime="2"
                           class="cselectorImageUpload" btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
                           extension="xls,xlsx" href="action/examQuestion/importExcel"
                           file="list" otherinfo="getInfoList"/></td>
                <td><a class="btn btn a_success fl export"><span>模板下载</span></a></td>
            </tr>
        </table>
    </form>

    <form id="exportForm" class="hide" method="post">
        <input type="submit" class="exportSubmit"/>
    </form>

    <div class="lzTable">
        <table id="lzTable"></table>
    </div>
</div>

</body>
</html>
package com.simbest.boot.exam.examOnline.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.dto.ExamEffectDto;
import com.simbest.boot.exam.examOnline.model.*;
import com.simbest.boot.exam.examOnline.repository.ExamSummaryRepository;
import com.simbest.boot.exam.examOnline.service.*;
import com.simbest.boot.exam.examOnline.util.ExamOnlineTool;
import com.simbest.boot.exam.examOnline.util.SyncTool;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.repository.ExamWorkRepository;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.flow.model.SysTaskInfo;
import com.simbest.boot.exam.flow.service.ISysTaskInfoService;
import com.simbest.boot.exam.message.service.IMessageService;
import com.simbest.boot.exam.sms.sevice.ILoginService;
import com.simbest.boot.exam.todo.model.UsTodoModel;
import com.simbest.boot.exam.todo.service.IUsTodoModelService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.wfquey.service.IQueryDictValueService;
import com.simbest.boot.mq.util.Exceptions;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.sys.model.SysDictValue;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.simbest.boot.exam.util.Constants.*;

/**
 * 用途：考试信息模块--考试汇总信息service
 * 作者：gy
 * 时间: 2021-02-01 10:42
 */
@Slf4j
@Service
public class ExamSummaryServiceImpl extends LogicService<ExamSummary, String> implements IExamSummaryService {
    private ExamSummaryRepository repository;

    @Autowired
    public ExamSummaryServiceImpl(ExamSummaryRepository repository, IQueryDictValueService queryDictValueService) {
        super(repository);
        this.repository = repository;
        this.queryDictValueService = queryDictValueService;
    }

    private final IQueryDictValueService queryDictValueService;

    @Autowired
    private IExamRangeService examRangeService;

    @Autowired
    private IExamInfoService examInfoService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private IExamWorkService examWorkService;

    @Autowired
    private ExamWorkRepository examWorkRepository;

    @Autowired
    private IUsTodoModelService usTodoModelService;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private ExamOnlineTool examOnlineTool;

    @Autowired
    private IExamAttributeService examAttributeService;

    @Autowired
    private IExamTaskService examTaskService;

    @Autowired
    private ISysTaskInfoService taskInfoService;

    @Setter(onMethod_ = {@Autowired})
    private SyncTool syncTool;

    @Autowired
    private IExamRangeGroupService examRangeGroupService;

    @Autowired
    private IExamRangeUserInfoService examRangeUserInfoService;

    @Autowired
    private IExamAnswerNotesService examAnswerNotesService;

    @Autowired
    private IExamAnnualQuarterInfoService examAnnualQuarterInfoService;

    @Autowired
    private UumsSysAppApi uumsSysAppApi;
    @Autowired
    private ISysOperateLogService operateLogService;
    @Autowired
    private IMessageService iMessageService;
    @Autowired
    private ILoginService iLoginService;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    private static final String SQL_ID = "exam_073ED14BF4D4FF0AE0605C0AA1521A76";


    @Value("${app.kf.users}")
    public String kfUsers;

    @Value("${app.host.port.inner}")
    public String hostPost;


    /**
     * 保存考试汇总信息
     *
     * @param o 表单数据
     * @return 返回入库数据
     */
    @Override
    public ExamSummary saveExamSummary(ExamSummary o) {
        // 保存汇总信息
        ExamSummary summaryInfo;
        if (StringUtils.isEmpty(o.getId())) {
            summaryInfo = this.insert(o);
        } else {
            summaryInfo = this.update(o);
        }
        return summaryInfo;
    }

    /**
     * 获取考试汇总详情信息
     *
     * @param id 考试信息外键
     * @return 返回考试汇总详情信息
     */
    @Override
    public ExamSummary findExamSummaryInfo(String id) {
        /* 1、获取考试范围信息 */
        ExamSummary summary = findById(id);
        Assert.notNull(summary, "考试汇总信息不存在!");
        /* 2、获取实考人数信息 */
        Integer actualExamNum = examInfoService.countActualExamNum(summary.getExamCode());
        summary.setActualExamNum(actualExamNum);
        return summary;
    }

    /**
     * 根据考试编码获取考试汇总信息详情
     *
     * @param examCode
     */
    @Override
    public Map<String, Object> findExamSummaryInfoByExamCode(String examCode) {
        HashMap<String, Object> map = new HashMap<>();
        Specification<ExamSummary> spec = Specifications.<ExamSummary>and()
                .eq("examCode", examCode)
                .eq("enabled", true)
                .build();
        ExamSummary examSummary = findOne(spec);
        LocalDateTime examEndTime = examSummary.getExamEndTime();
        String format = cn.hutool.core.date.DateUtil.format(examEndTime, "yyyy-MM-dd HH:mm:ss");
        String s = format.replace("-", "/");
        map.put("examEndTime", s);
        return map;
    }

    @Override
    public ExamSummary findExamSummaryByCode(String code) {
        Specification<ExamSummary> spec = Specifications.<ExamSummary>and()
                .eq("examCode", code)
                .eq("enabled", true)
                .build();
        return findOne(spec);
    }

    /**
     * 查询考试汇总信息列表
     *
     * @param page       页数
     * @param size       条目数
     * @param direction  排序规则(asc/desc)
     * @param properties 排序属性
     * @param o          表单查询条件
     * @return 分页后的考试汇总信息
     */
    @Override
    public Page<ExamSummary> findExamSummaryList(int page, int size, String direction, String properties, ExamSummary o) {
        IUser currentUser = SecurityUtils.getCurrentUser();
        // 设置默认排序规则
        if (StringUtils.isEmpty(direction)) {
            direction = "desc";
        }
        if (StringUtils.isEmpty(properties)) {
            properties = "createdTime";
        }
        // 构筑分页对象
        Pageable pageable = getPageable(page, size, direction, properties);
        Page<ExamSummary> pages = null;
        // 需要对【梁洁】用户做特殊处理
        if (currentUser.getUsername().equals(USERNAME_MANAGER1)) {
            // 构筑查询条件
            Specification<ExamSummary> spec = Specifications
                    .<ExamSummary>and()
                    .like(StringUtils.isNotEmpty(o.getExamName()), "examName", "%" + o.getExamName() + "%")
                    .like(StringUtils.isNotEmpty(o.getExamCode()), "examCode", o.getExamCode())
                    .predicate(Specifications.<ExamSummary>or()
                            .eq("examCode", Constants.EXAM_CODE_OFFICE_LY)
                            .eq("examCode", Constants.EXAM_CODE_BRANCH_LY)
                            .build())
                    .build();
            pages = findAll(spec, pageable);
        } else {
            // 构筑查询条件  根据登录用户查询对应的考试信息
            Specification<ExamSummary> spec = Specifications
                    .<ExamSummary>and()
                    .like(StringUtils.isNotEmpty(o.getExamName()), "examName", "%" + o.getExamName() + "%")
                    .like(StringUtils.isNotEmpty(o.getExamCode()), "examCode", "%" + o.getExamCode() + "%")
                    .eq("creator", currentUser.getUsername())
                    .build();
            pages = findAll(spec, pageable);
        }
//        // 构筑分页对象
//        Pageable pageable = getPageable(page, size, direction, properties);
//        Page<ExamSummary> pages = findAll(spec, pageable);
        // 计算实际参加考试人数
        for (ExamSummary examSummary : pages.getContent()) {
            // 查询考试待办人数
            int workByTask = examWorkRepository.countExamWorkByTask(examSummary.getExamCode());
            examSummary.setJoinExamNum(workByTask);
            // 查询考试统一待办人数
            int workByTodo = examWorkRepository.countExamWorkByTodo(examSummary.getExamCode());
            examSummary.setTodoExamNum(workByTodo);
            // 查询考试参与人数
            Integer actualExamNum = examInfoService.countActualExamNum(examSummary.getExamCode());
            examSummary.setActualExamNum(actualExamNum);
            // 查看定时器状态
            Specification<ExamTask> build = Specifications.<ExamTask>and()
                    .eq("examCode", examSummary.getExamCode())
                    .eq("enabled", QUERY_VALID_CONDITION).build();
            ExamTask one = examTaskService.findOne(build);
            if (one != null) {
                examSummary.setTaskStatus(one.getTaskStatus());
            } else {
                examSummary.setTaskStatus(0);
            }
        }
        return pages;
    }

    /**
     * 手机端控制飘窗展示接口
     * @param currentUserCode   当前登录人
     * @param source            来源 ， 默认都是MOBILE
     * @return
     */
    @Override
    public ExamEffectDto findEffectiveExam(String currentUserCode, String source) {
        if (SOURCE_M.equals(source)) {
            loginUtils.manualLogin(currentUserCode, APP_CODE);
        }
        //查询不同来源的信息配置
        Specification<ExamSummary> summarySpec = Specifications
                .<ExamSummary>and()
                .eq("enabled" , Boolean.TRUE)
                .eq(StrUtil.equals(source , SOURCE_M), "appEnabled", true) // 移动端校验手机开关是否打开
                .eq(StrUtil.equals(source , SOURCE_SMS), "smsEnabled" , Boolean.TRUE)
                .eq(StrUtil.equals(source , SOURCE_P ), "pcEnabled", true)// pc端校验pc端开关是否打开
                .le("examStartTime" , LocalDateTime.now())
                .ge("examEndTime" , LocalDateTime.now())
                .build();
        List<ExamSummary> summaryList = findAllNoPage(summarySpec, Sort.by(Sort.Direction.DESC, "weightValue"));
        Boolean hasTodo = Boolean.FALSE;
        Boolean isJb = Boolean.FALSE;
        SysTaskInfo taskInfo = null;
        ExamSummary newSummary = null;
        IUser iUser = SecurityUtils.getCurrentUser();
        if (CollectionUtil.isNotEmpty(summaryList)) {
            for (ExamSummary examSummary : summaryList) {
                //如果是简报需要特殊处理。
                if (StrUtil.equals(examSummary.getExamCode() , "jb")) {
                    List<SysTaskInfo> taskInfos = taskInfoService.findTaskByUserName(SecurityUtils.getCurrentUserName());
                    if (CollectionUtil.isNotEmpty(taskInfos)) {
                        hasTodo = Boolean.TRUE;
                        isJb = Boolean.TRUE;
                        taskInfo = taskInfos.get(0);
                    }
                }
                //不推送待办的时候，手机办公将默认展示悬浮，办理后将不展示
                else if (null != examSummary.getNoTodo() && examSummary.getNoTodo() ) {
                    if (StrUtil.equals(kfUsers , "no") || kfUsers.contains(iUser.getUsername())) {
                        ExamInfo examInfo = examInfoService.findByExamInfo(iUser.getUsername(), examSummary.getExamCode());
                        if (null != examInfo) {
                            hasTodo = Boolean.FALSE;
                        } else {
                            hasTodo = Boolean.TRUE;
                        }
                    } else {
                        hasTodo = Boolean.FALSE;
                    }
                }else {
                    /**根据是否有待办活已办判断当前登录人是否有答题权限*/
                    hasTodo = examWorkService.checkIsNotDone(iUser.getUsername(), examSummary.getExamCode());
                }
                if (hasTodo) {
                    newSummary = examSummary;
                    break;
                }
            }
        }
        /*---------------------3、根据校验结果，构造返回信息---------------------*/
            // 通过校验，显隐控制为true，显示相应悬浮框，并将相关数据返回
        log.warn("是否存在待办：{} , 存在的内容是：{}" , hasTodo , null!=newSummary ? JacksonUtils.obj2json(newSummary) : "空");
        if (hasTodo) {
            String appExamUrl = newSummary.getAppExamUrl();
            if (isJb && null != taskInfo) {
                appExamUrl = newSummary.getAppExamUrl() + "?id=" + taskInfo.getId() + "&phoneNumber=" + rsaEncryptor.encrypt(taskInfo.getApplyPhone()) ;
            }
            return ExamEffectDto.builder()
                    .showFlag(true)
                    .examCode(newSummary.getExamCode())
                    .appExamUrl(appExamUrl)
                    .pcExamUrl(newSummary.getPcExamUrl())
                    .appImageUrl(newSummary.getAppImageUrl())
                    .pcImageUrl(newSummary.getPcImageUrl())
                    .title(newSummary.getExamName())
                    .appcode("exam")
                    .build();
        } else {
            // 未通过校验，显隐控制为false，不显示相应悬浮框
            return ExamEffectDto.builder().showFlag(false).appcode("exam").build();
        }
    }

    /**
     * 根据试卷编码查询考试信息
     * @param examAppCode 试卷编码
     * @return
     */
    @Override
    public ExamSummary findInfoByExamAppCode(String examAppCode) {
        List<ExamSummary> examSummarys =  repository.findByExamAppCode(examAppCode);
        return CollectionUtil.isNotEmpty(examSummarys) ? examSummarys.get(0) : null;
    }


    /**
     * 小程序考试校验是否有权限功能开发
     * @param currentUserCode
     * @param source
     * @param examCode
     * @return
     */
    @Override
    public ExamEffectDto findEffectiveExamSms(String currentUserCode, String source, String examCode) {
        Specification<ExamSummary> summarySpec = Specifications
                .<ExamSummary>and()
                .eq("examCode", examCode)
                .eq("smsEnabled", true)
                .le("examStartTime" , LocalDateTime.now())
                .ge("examEndTime" , LocalDateTime.now())
                .build();
        List<ExamSummary> summaryList = findAllNoPage(summarySpec, Sort.by(Sort.Direction.DESC, "createdTime"));
        if (CollectionUtil.isNotEmpty(summaryList)) {
            ExamSummary examSummary = summaryList.get(0);
            return ExamEffectDto.builder()
                    .showFlag(true)
                    .examCode(examSummary.getExamCode())
                    .appExamUrl(examSummary.getAppExamUrl())
                    .pcExamUrl(examSummary.getPcExamUrl())
                    .appImageUrl(examSummary.getAppImageUrl())
                    .pcImageUrl(examSummary.getPcImageUrl())
                    .title(examSummary.getExamName())
                    .appcode("exam")
                    .build();
        } else {
            // 未通过校验，显隐控制为false，不显示相应悬浮框
            return ExamEffectDto.builder().showFlag(false).appcode("exam").build();
        }
    }

    /**
     * 根据考试编码推送统一待办信息
     *
     * @param examCode 考试编码
     */
    @Override
    public void sendTodo(String examCode) {
        // 获取考试汇总信息，查看是否已推送过统一待办
        ExamSummary summaryInfo = findExamSummaryByCode(examCode);
        Assert.notNull(summaryInfo, "考试信息为空!");
        Assert.isTrue(StringUtils.isNotEmpty(summaryInfo.getWorkType()), "未指定统一待办类型，不可推送统一待办!");

        //如果是洛阳满意度评价问卷，则可以重复推送。
        if (!examCode.equals(EXAM_CODE_BRANCH_LY) && !examCode.equals(EXAM_CODE_OFFICE_LY)) {
            Assert.isTrue(!summaryInfo.getTodoFlag(), "统一待办已推送，不可重复推送!");
        }
        // 获取本次考试参与人员
        // 获取待办类型
        String workType = summaryInfo.getWorkType();
        Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
        //存储存放待办的list
        List<ExamWork> todoWorkList = Lists.newArrayList();
        //判断是否是洛阳满意度评价的考试
        if (workType.equals(WORK_TYPE_E) || workType.equals(WORK_TYPE_F)) {
            Specification<ExamWork> spec = Specifications
                    .<ExamWork>and()
                    .eq("enabled", 1)
                    .eq("workType", workType)
                    .eq("isTodoFlag", "0")
                    .eq("examCode", examCode)
                    .build();
            List<ExamWork> workList = examWorkService.findAllNoPage(spec);
            Assert.isTrue(!workList.isEmpty(), "尚未产生考试待办，无法推送统一待办！");
            for (ExamWork work : workList) {
                //获取最新的满意度问卷考试的年度季度信息
                LocalDateTime createdTime = work.getCreatedTime();
                String s = createdTime.toString();
                String createdTime2 = s.substring(0, 10);
                ExamAnnualQuarterInfo annualQuarterInfo = examAnnualQuarterInfoService.findAnnualQuarterInfo(examCode, createdTime2);
                //洛阳满意度问卷调查推送统一代办业务 通过查询当前人是否有答题记录判断
                ExamQuestionUser byCreator = examAnswerNotesService.findByCreator(work.getTransactorCode(), annualQuarterInfo.getAnnualQuarterCode());
                if (byCreator == null) {
                    //针对没有进行核销操作的用户，并且没有参与过考试答题，则进行推发统一待办操作
                    todoWorkList.add(work);
                }
            }
        } else {
            //对未核销待办的工单推送统一待办
            List<ExamWork> workList = examWorkRepository.findTodoWork(examCode, workType, "0");
            Assert.isTrue(!workList.isEmpty(), "尚未产生考试待办，无法推送统一待办！");
            todoWorkList.addAll(workList);
        }
        // 推发统一待办
        log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>推发统一待办【{}】>>>>>>>>>>>>>>>>>>>>>", todoWorkList);
        // 异步推送统一待办
        syncTool.asyncSendUnifiedToDo(todoWorkList);
        log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>推送待办完成，总计推送" + todoWorkList.size() + "条");
        // 推送完毕，修改考试信息为已推送过统一待办
        summaryInfo.setTodoFlag(true);
        this.update(summaryInfo);
    }

    /**
     * 批量核销待办事项。
     *
     * @param examCode 考试编号。
     */
    @Override
    public void dealTodo(String examCode) {
        // 获取考试汇总信息，查看是否已推送过统一待办
        ExamSummary summaryInfo = findExamSummaryByCode(examCode);
        Assert.notNull(summaryInfo, "考试信息为空!");
        Assert.isTrue(StringUtils.isNotEmpty(summaryInfo.getWorkType()), "未指定统一待办类型，不可核销统一待办!");

        // 获取待办类型
        String workType = summaryInfo.getWorkType();
        //对推送过统一待办的工单核销统一待办
        List<ExamWork> workList = examWorkRepository.findTodoWork(examCode, workType, "1");
        Assert.isTrue(!workList.isEmpty(), "暂无考试待办或已全部核销！");

        // 异步核销统一待办
        syncTool.asyncDealWith(workList);
    }

    /**
     * @desc 定时推送统一待办
     * <AUTHOR>
     */
    @Override
    public void sendTodoTask(String examCode) {
        // 获取考试汇总信息，查看是否已推送过统一待办
        ExamSummary summaryInfo = findExamSummaryByCode(examCode);
        Assert.notNull(summaryInfo, "考试信息为空!");
        Assert.isTrue(StringUtils.isNotEmpty(summaryInfo.getWorkType()), "未指定统一待办类型，不可推送统一待办!");

        //如果是洛阳满意度评价问卷，则可以重复推送。
        if (!examCode.equals(EXAM_CODE_BRANCH_LY) && !examCode.equals(EXAM_CODE_OFFICE_LY)) {
            Assert.isTrue(!summaryInfo.getTodoFlag(), "统一待办已推送，不可重复推送!");
        }
        // 获取本次考试参与人员
        // 获取待办类型
        String workType = summaryInfo.getWorkType();
        Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
        Specification<ExamWork> spec = Specifications
                .<ExamWork>and()
                .eq("enabled", true)
                .eq("workType", workType)
                .eq("isTodoFlag", "0")
                .build();
        List<ExamWork> workList = examWorkService.findAllNoPage(spec);
        Assert.isTrue(!workList.isEmpty(), "尚未产生考试待办，无法推送统一待办！");
        List<ExamWork> todoWorkList = Lists.newArrayList();
        for (ExamWork work : workList) {
            String workType1 = work.getWorkType();
            //查询是否存在已经核销的统一待办
            UsTodoModel allTypeStatus = usTodoModelService.findAllTypeStatus(work.getTransactorCode(), workType1);

            if (allTypeStatus == null) {
                //判断是否是洛阳满意度评价的考试
                if (workType1.equals(WORK_TYPE_E) || workType1.equals(WORK_TYPE_F)) {
                    //获取最新的满意度问卷考试的年度季度信息
                    LocalDateTime createdTime = work.getCreatedTime();
                    String s = createdTime.toString();
                    String createdTime2 = s.substring(1, 11);
                    ExamAnnualQuarterInfo annualQuarterInfo = examAnnualQuarterInfoService.findAnnualQuarterInfo(examCode, createdTime2);
                    //洛阳满意度问卷调查推送统一代办业务 通过查询当前人是否有答题记录判断
                    ExamQuestionUser byCreator = examAnswerNotesService.findByCreator(work.getTransactorCode(), annualQuarterInfo.getAnnualQuarterCode());
                    if (byCreator == null) {
                        //针对没有进行核销操作的用户，并且没有参与过考试答题，则进行推发统一待办操作
                        todoWorkList.add(work);
                    }
                } else {
                    //获取当前人是否已经进行过答题操作
                    ExamInfo byExamInfo = examInfoService.findByExamInfo(work.getTransactorCode(), examCode);
                    if (byExamInfo == null) {
                        todoWorkList.add(work);
                    }
                }
            }
        }
        // 推发统一待办
        examWorkService.sendUnifiedToDo(todoWorkList);
        log.warn("推送待办完成，总计推送" + todoWorkList.size() + "条");
        // 推送完毕，修改考试信息为已推送过统一待办
        summaryInfo.setTodoFlag(true);
        this.update(summaryInfo);
    }

    /**
     * 根据考试编码发送短信催办
     *
     * @param examCode 考试编码
     */
    @Override
    public void sendUrgeSms(String examCode) {
        // 获取考试汇总信息，查看是否已推送过统一待办
        ExamSummary summaryInfo = findExamSummaryByCode(examCode);
        Assert.notNull(summaryInfo, "考试信息为空!");
//        Assert.isTrue(StringUtils.isNotEmpty(summaryInfo.getWorkType()), "未指定统一待办类型，不可推送短信催办!");
        /**都可重复推送短信,
         //        if (!examCode.equals(EXAM_CODE_OFFICE_LY) && !examCode.equals(EXAM_CODE_BRANCH_LY) && !summaryInfo.getWorkType().equals(WORK_TYPE_H)){
         //            Assert.isTrue(!summaryInfo.getMessageFlag(), "短信催办已推送，不可重复推送!");
         //        }
         /** 1、获取没有办理的待办 */
        List<Map<String, Object>> unfinishedExam = examWorkService.findUnfinishedExamList(examCode);
        Assert.isTrue(!unfinishedExam.isEmpty(), "工单都已办理完毕，无需推送短信催办!");
        /* 2、推送催办短信 */
        for (Map<String, Object> examWorkMap : unfinishedExam) {
            /*
             * 推送短信,发短信所需信息:
             *  1.收短信方
             *  2.发短信方
             *  3.短信标题
             */
            examOnlineTool.sendShortMessage(examCode, String.valueOf(examWorkMap.get("ID")), String.valueOf(examWorkMap.get("TRANSACTOR_CODE")), String.valueOf(examWorkMap.get("TITLE")));
        }
        // 推送完毕，修改考试信息为已推送过短信催办
        summaryInfo.setMessageFlag(true);
        /**短信推送次数加1*/
        Integer numberOfMessage = summaryInfo.getNumberOfMessage();
        summaryInfo.setNumberOfMessage(numberOfMessage + 1);
        this.update(summaryInfo);
    }

    /**
     * 定时器用短信催办
     *
     * @param examCode
     */
    @Override
    public void sendUrgeSmsTask(String examCode) {
        // 获取考试汇总信息，查看是否已推送过统一待办
        ExamSummary summaryInfo = findExamSummaryByCode(examCode);
        Assert.notNull(summaryInfo, "考试信息为空!");
//        Assert.isTrue(StringUtils.isNotEmpty(summaryInfo.getWorkType()), "未指定统一待办类型，不可推送短信催办!");
//        Assert.isTrue(!summaryInfo.getMessageFlag(), "短信催办已推送，不可重复推送!");
        if (!examCode.equals(EXAM_CODE_OFFICE_LY) && !examCode.equals(EXAM_CODE_BRANCH_LY)) { //洛阳满意度考试催办可重复推
            Assert.isTrue(!summaryInfo.getMessageFlag(), "短信催办已推送，不可重复推送!");
        }
        /* 1、获取没有办理的待办 */
        List<Map<String, Object>> unfinishedExam = examWorkService.findUnfinishedExamList(examCode);
        Assert.isTrue(!unfinishedExam.isEmpty(), "工单都已办理完毕，无需推送短信催办!");
        /* 2、推送催办短信 */
        for (Map<String, Object> examWorkMap : unfinishedExam) {
            /*
             * 推送短信,发短信所需信息:
             *  1.收短信方
             *  2.发短信方
             *  3.短信标题
             */
            examOnlineTool.sendShortMessage(examCode, String.valueOf(examWorkMap.get("ID")), String.valueOf(examWorkMap.get("TRANSACTOR_CODE")), String.valueOf(examWorkMap.get("TITLE")));
        }
        // 推送完毕，修改考试信息为已推送过短信催办
        summaryInfo.setMessageFlag(true);
        this.update(summaryInfo);
    }

    /**
     * 获取应用有效token
     *
     * @param appcode 应用编码
     * @return 有效token
     */
    @Override
    public Map<String, Object> findAccessToken(String currentUserCode, String source, String appcode) {
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap = HttpClient.post(hostPost + "/" + appcode + "/oauth/token?grant_type=client_credentials&scope=all&client_id=simbest_maip&client_secret=338d6311c8efad8314b9212860adb12e")
                .param("username", currentUserCode)
                .asBean(Map.class);
        boolean flag = false;
        for (String key : resultMap.keySet()) {
            if ("access_token".equals(key)) {
                flag = true;
                break;
            }
        }
        if (!flag) {
            resultMap.put("access_token", "");
        }
        return resultMap;
    }


    /**
     * 根据考试编码推发待办
     *
     * @param examCode 考试编码
     */
    @Override
    public void createExamWork(String examCode) {
        /* ---------------------1.根据考试编码获取考试信息---------------------- */
        ExamSummary examSummary = findExamSummaryByCode(examCode);
        Assert.notNull(examSummary, "考试信息为空!");
        Assert.isTrue(StringUtils.isNotEmpty(examSummary.getWorkType()), "未指定统一待办类型，不可推送待办!");

        //如果是洛阳满意度评价问卷则可重复推待办
        if (!examSummary.getExamCode().equals(EXAM_CODE_OFFICE_LY) && !examSummary.getExamCode().equals(EXAM_CODE_BRANCH_LY)) {
            Assert.isTrue(!examSummary.getWorkFlag(), "考试信息已产生过待办，不可重复操作!");
        }

        /* ---------------------2.根据考试信息获取考试范围---------------------- */
        Iterator<ExamRange> rangeIter = examRangeService.findBySummaryId(examSummary.getId());


        List<Map<String, Object>> userList = new ArrayList<>();
        // Assert.isTrue(rangeIter.hasNext(), "未指定考试范围!");
        /* ---------------------3.根据考试范围获取到考试范围内的人，并生成对应的待办工单---------------------- */
        ExamRange examRange = new ExamRange();
        String title = "";
        while (rangeIter.hasNext()) {
            examRange = rangeIter.next();
            /* ---------------------3.1获取考试范围内的人---------------------- */
            userList = findExamUserList(examRange.getCompanyName());

            //  生成每个考试范围内采用的待办标题
            Assert.isTrue(StringUtils.isNotEmpty(examRange.getExamPaperCode()), "未指定考试采用试卷信息");
            ExamAttribute examPaper = examAttributeService.getExamAttributeByExamAppCode(examRange.getExamPaperCode());
            title = examPaper.getExamName(); //默认采用试卷作为考试标题

        }
        //根据考试编码查询参考人员信息  洛阳满意度考试项目
        if (examCode.equals(EXAM_CODE_OFFICE_LY) | examCode.equals((EXAM_CODE_BRANCH_LY))) {
            List<Map<String, Object>> rangeGroupUser = examRangeGroupService.findUserByExamAppCode(examCode);
            userList.addAll(rangeGroupUser);
            // 根据username对userList去重
            List<Map<String, Object>> newUserList = userList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(m -> m.get("USERNAME").toString()))
                            ), ArrayList::new
                    )
            );
            /* ---------------------3.2生成对应的待办工单---------------------- */
            for (Map<String, Object> map : newUserList) {
                if (map != null && !map.isEmpty()) {
                    // 准备待办参数

                    String username = map.get("USERNAME") != null ? map.get("USERNAME").toString() : "";

                    String trueName = map.get("TRUENAME") != null ? map.get("TRUENAME").toString() : "";
                    //    title = (String) map.get("title");
                    String examName = examSummary.getExamName();//考试名称作为待办标题
                    //根据考试名称查询试卷的编码，存入待办信息表中,以便在获取通用试卷模板
                    ExamRangeGroup examRangeGroup1 = examRangeGroupService.findByExamName(examName);
                    String examAppCode = examRangeGroup1.getExamAppCode();
                    // 获取待办类型
                    String workType = examSummary.getWorkType();
                    Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
                    syncTool.createExamWork(examCode, username, trueName, examRange.getCompanyName(), examName, workType, examAppCode);
                }
            }
            // 待办处理完毕以后，修改考试状态为已产生过待办工单
            examSummary.setWorkFlag(true);
            ExamSummary update = this.update(examSummary);
            //如果成功推送待办则保存考试的年度季度信息 以次作为标记来统计结果
            if (examCode.equals(EXAM_CODE_BRANCH_LY) | examCode.equals(EXAM_CODE_OFFICE_LY)) {
                examAnnualQuarterInfoService.saveAnnualQuarterInfo(examCode);
            }
        } else {
            //根据考试编码查询群组编码，再根据群组编码查询该群组下的人员(存在一个考试多个群组的情况)
            Iterable<ExamRangeGroup> examRangeGroupByExamCode = examRangeGroupService.findAllNoPage(Specifications.<ExamRangeGroup>and().eq("examCode", examCode).build());
            // 通过考试群组 异步创建考试待办
            syncTool.asyncCreateExamWorkByExamRange(examCode, examSummary.getExamName(), examSummary.getWorkType(), examRangeGroupByExamCode);
            /* ---------------------3.2生成对应的待办工单---------------------- */

            // 待办处理完毕以后，修改考试状态为已产生过待办工单
            examSummary.setWorkFlag(true);
            ExamSummary update = this.update(examSummary);

        }
    }

    // 定时器每个季度推送调查问卷
    @Override
    public void createExamWorkTask(String examCode) {
        /* ---------------------1.根据考试编码获取考试信息---------------------- */
        ExamSummary examSummary = findExamSummaryByCode(examCode);
        Assert.notNull(examSummary, "考试信息为空!");
        Assert.isTrue(StringUtils.isNotEmpty(examSummary.getWorkType()), "未指定统一待办类型，不可推送待办!");
        /* ---------------------2.根据考试信息获取考试范围---------------------- */
        Iterator<ExamRange> rangeIter = examRangeService.findBySummaryId(examSummary.getId());
        //根据考试编码查询参考人员信息
        List<Map<String, Object>> rangeGroupUser = examRangeGroupService.findUserByExamAppCode(examSummary.getExamCode());

        List<Map<String, Object>> userList = new ArrayList<>();
        // Assert.isTrue(rangeIter.hasNext(), "未指定考试范围!");
        /* ---------------------3.根据考试范围获取到考试范围内的人，并生成对应的待办工单---------------------- */
        ExamRange examRange = new ExamRange();
        String title = "";
        while (rangeIter.hasNext()) {
            examRange = rangeIter.next();
            /* ---------------------3.1获取考试范围内的人---------------------- */
            userList = findExamUserList(examRange.getCompanyName());

            //  生成每个考试范围内采用的待办标题
            Assert.isTrue(StringUtils.isNotEmpty(examRange.getExamPaperCode()), "未指定考试采用试卷信息");
            ExamAttribute examPaper = examAttributeService.getExamAttributeByExamAppCode(examRange.getExamPaperCode());
            title = examPaper.getExamName(); //默认采用试卷作为考试标题
        }
        userList.addAll(rangeGroupUser);
        // 根据username对userList去重
        List<Map<String, Object>> newUserList = userList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(m -> m.get("USERNAME").toString()))
                        ), ArrayList::new
                )
        );
        /* ---------------------3.2生成对应的待办工单---------------------- */
        for (Map<String, Object> map : newUserList) {
            if (map != null && !map.isEmpty()) {
                // 准备待办参数
                String username = map.get("USERNAME") != null ? map.get("USERNAME").toString() : "";
                String trueName = map.get("TRUENAME") != null ? map.get("TRUENAME").toString() : "";
                //  title = (String) map.get("title");
                String examName = examSummary.getExamName();//考试名称作为待办标题
                //根据考试名称查询试卷的编码，存入待办信息表中,以便在获取通用试卷模板
                ExamRangeGroup examRangeGroup1 = examRangeGroupService.findByExamName(examName);
                String examAppCode = examRangeGroup1.getExamAppCode();
                // 获取待办类型
                String workType = examSummary.getWorkType();
                Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
                syncTool.createExamWork(examCode, username, trueName, examRange.getCompanyName(), examName, workType, examAppCode);
            }
        }
        // 待办处理完毕以后，修改考试状态为已产生过待办工单
        examSummary.setWorkFlag(true);
        ExamSummary update = this.update(examSummary);

        //如果成功推送待办则保存考试的年度季度信息 以次作为标记来统计结果
        examAnnualQuarterInfoService.saveAnnualQuarterInfo(examCode);
    }

    /**
     * 获取应考人数
     *
     * @param rangeList 考试范围集合
     * @return 返回应考人数
     */
    @Override
    public Integer findJoinExamNum(List<ExamRange> rangeList) {
        // 获取对应orgCode
        List<String> companyNames = Lists.newArrayList();
        for (ExamRange examRange : rangeList) {
            // 由于queryNamedParameterForList对于in查询不会拼接双引号，所以这里手动处理双引号拼接操作
            companyNames.add(examRange.getCompanyName());
        }
        // 根据orgCode查询组织下的人
        Map<String, Object> map = Maps.newHashMap();
        map.put("companyNames", companyNames);
        String sql = "SELECT count(distinct up.username) as joinExamNum " +
                "FROM uums.V_USER_ORG_POSITION up " +
                "WHERE  up.displayorgname in (:companyNames) " +
                "AND up.positionName != '一线员工'" +
                "AND up.userType = '1'";
        log.debug("Query sql is : {} and parameter is {}", sql, map);
        List<Map<String, Object>> userList = customDynamicWhere.queryNamedParameterForList(sql, map);

        Map<String, Object> resultMap = userList.get(0);
        return Integer.valueOf(String.valueOf(resultMap.get("joinExamNum")));
    }

    @Override
    public List<ExamSummary> findLeftTree(String examCode) {
        IUser currentUser = SecurityUtils.getCurrentUser();
        List<ExamSummary> result = null;
        //todo 如果是梁洁等特殊人员登录 展示特定树结构
        if (Constants.USERNAME_MANAGER1.equals(currentUser.getUsername())) {
            Specification<ExamSummary> build = Specifications.<ExamSummary>and().eq("enabled", Constants.QUERY_VALID_CONDITION)
                    .predicate(Specifications.<ExamSummary>or()
                            .eq("examCode", EXAM_CODE_BRANCH_LY)
                            .eq("examCode", EXAM_CODE_OFFICE_LY)
                            .build())
                    .build();
            result = this.findAllNoPage(build , Sort.by(Sort.Direction.DESC , "createdTime"));
        } else {
            Specification<ExamSummary> build = Specifications.<ExamSummary>or()
                    .like(StringUtils.isNotBlank(examCode), "examCode", String.format("%%%s%%", examCode))
                    .like(StrUtil.isNotEmpty(examCode) , "examName" , String.format("%%%s%%", examCode))
                    .build();
            result = this.findAllNoPage(build  , Sort.by(Sort.Direction.DESC , "createdTime"));
        }
        return result;
    }

    /**
     * 校验当前登录人是否有权限参加考试
     *
     * @param summaryList 考试信息集合（已根据权重进行过排序）
     * @return 返回Map，包含两个值：
     * 1、flag boolean值 true为有权限，false为无权限
     * 2、summaryInfo ExamSummary对象，通过校验时，返回有权限的考试信息，未通过校验则返回null
     */
    private Map<String, Object> checkEffectiveExam(List<ExamSummary> summaryList) {
        Map<String, Object> resultMap = Maps.newHashMap();
        ExamSummary summaryInfo = null;
        boolean flag = false;
        /*------------------------1、获取当前用户信息---------------------*/
        // 获取当前登录人信息
        IUser iUser = SecurityUtils.getCurrentUser();
        /*------------------------2、校验考试信息，校验当前人是否处于考试范围内，当前时间是否在考试时间范围内，当前人是否已经提交过答题结果---------------------*/
        // 校验考试信息
        if (null != summaryList && !summaryList.isEmpty()) {
            // 获取用户归属考试范围，判断用户是否归属当前考试范围
            for (ExamSummary examSummary : summaryList) {
                // TODO iUser.getBelongCompanyCode() 当前考试范围默认以公司为单位，相关部门科室等组织开发任务紧急暂时不做支持，需要后续调整
//                ExamRange rangeInfo = null;
//                if ("03".equals(iUser.getBelongCompanyTypeDictValue())) {
//                    // 县公司获取当前人父公司code
//                    rangeInfo = examRangeService.findByCompanyCodeAndSummaryId(examSummary.getId(), iUser.getBelongCompanyCodeParent());
//                } else {
//                    rangeInfo = examRangeService.findByCompanyCodeAndSummaryId(examSummary.getId(), iUser.getBelongCompanyCode());
//                }
                /**根据US_EXAM_RANGE_USER_INFO表中的userName和examName信息判断当前登录人是否有答题权限*/
                //ExamRangeUserInfo examRangeUserInfo = examRangeUserInfoService.findByUserName(iUser.getUsername(), examSummary.getExamName());
                /**根据是否有待办活已办判断当前登录人是否有答题权限*/
                boolean isHaveJurisdiction = examWorkService.findByTransactorCodeAndExamCode(iUser.getUsername(), examSummary.getExamCode());
                // 获取到相关考试范围，则采用当前考试范围配置信息
                if (isHaveJurisdiction) {
                    // 校验当前时间是否归属于考试起止时间范围内
                    LocalDateTime now = LocalDateTime.now();
                    DateUtil.localDateTime2Date(now);
                    boolean isIn = cn.hutool.core.date.DateUtil.isIn(DateUtil.localDateTime2Date(now),
                            DateUtil.localDateTime2Date(examSummary.getExamStartTime()), DateUtil.localDateTime2Date(examSummary.getExamEndTime()));
                    if (isIn) {
                        // 当前时间校验通过，判断用户是否已经进行过答题操作
                        ExamInfo examInfo = examInfoService.findExamInfo(iUser.getUsername(), examSummary.getExamCode(), null);
                        if (null == examInfo || !examInfo.getIsFinishExam()) {
                            summaryInfo = examSummary;
                            flag = true;
                        }
                    }
                    break;
                }
            }
        }
        if (flag) {
            log.debug("====================================当前登陆人【{}】通过考试校验，目前读取的考试信息名称为【{}============================",
                    iUser.getUsername(), summaryInfo.getExamName());
        } else {
            log.debug("====================================当前登陆人【{}】未通过考试校验================================", iUser.getUsername());
        }
        resultMap.put("flag", flag);
        resultMap.put("summaryInfo", summaryInfo);
        return resultMap;
    }


    /**
     * 根据公司名称获取公司下的一般员工
     *
     * @param companyName 公司名称（根据名称可查询市公司及县公司的员工，仅靠code不易查询）
     * @return 返回视图中的该公司下的一线员工
     */
    private List<Map<String, Object>> findExamUserList(String companyName) {
        String sql = "SELECT distinct up.truename as TRUENAME, up.username as USERNAME " +
                "FROM uums.V_USER_ORG_POSITION up " +
                "WHERE  up.displayName LIKE concat(:companyName,'%') " +
                "AND up.positionName != '一线员工' " +
                "AND up.userType = '1' " +
                "AND up.username not in(select username from TEMP) ";
        Map<String, Object> map = Maps.newHashMap();
        map.put("companyName", companyName);
        return customDynamicWhere.queryNamedParameterForList(sql, map);
    }

    /**
     * @desc 根据待办类型查询
     * <AUTHOR>
     */
    @Override
    public ExamSummary findExamSummaryByWorkType(String workType) {
        return repository.findByWorkType(workType);
    }

    /**
     * 根据考试编码推发短信问卷
     *
     * @param examCode 考试编码
     */
    @Override
    public Map<String, String> createExamSmsWork(String examCode) {
        SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, "hadmin");
        if (simpleApp != null) {
            Assert.isTrue(simpleApp.getIsSendMsg(),"未打开推送短信开关！");
        }
        /* ---------------------1.根据考试编码获取考试信息---------------------- */
        ExamSummary examSummary = findExamSummaryByCode(examCode);
        Assert.notNull(examSummary, "考试信息为空!");
        /* ---------------------2.根据考试信息获取考试范围---------------------- */
        /* ---------------------3.根据考试范围获取到考试范围内的人，并生成对应的待办工单---------------------- */
        //根据考试编码查询群组编码，再根据群组编码查询该群组下的人员(存在一个考试多个群组的情况)
        Iterable<ExamRangeGroup> examRangeGroupByExamCode = examRangeGroupService.findExamRangeGroupByExamCode(examCode);

        // 获取定制的短信内容
        String msg = iMessageService.findMessageContentByExamCode(examCode);
        String defaultMsg = String.format("【综合考试管理系统】您好！您有一个待处理的考试/测评/问卷，名称：%s，请及时处理！", examSummary.getExamName());
        msg = StringUtils.isBlank(msg) ? defaultMsg : msg;
        String shortUrl = StringUtils.isBlank(examSummary.getSmsExamUrl()) ? "" : String.format("点击下方链接直达：%s", iLoginService.ConvertShortUrl(examSummary.getSmsExamUrl()));
        String content = msg + shortUrl;

        Map<String, String> result = Maps.newHashMap();
        for (ExamRangeGroup examRangeGroup : examRangeGroupByExamCode) {
            String groupId = examRangeGroup.getGroupId();
            //确保查询到所有的群组人员
            JsonResponse byGroupId = examRangeUserInfoService.findByGroupId(1, 30000, groupId, null, null, null);
            Map<String, Object> data = (Map<String, Object>) byGroupId.getData();
            List<Map<String, Object>> userInfos = (List<Map<String, Object>>) data.get("content");
            if (userInfos.size() > 5000) {
                log.warn("当前群组【{}】的人员超过5000人，建议通过短信平台发送短信，请检查！", groupId);
                throw new IllegalStateException("当前考试群组的人员超过5000人，建议通过短信平台发送短信，请检查！");
            }
            //发送短信
            userInfos.parallelStream().filter(CollectionUtil::isNotEmpty).map(map -> map.get("USERNAME") != null ? map.get("USERNAME").toString() : "").forEach(username -> {
                boolean flag = examOnlineTool.sendShortMessageQuestInfo(username, content);
                // 记录短信发送
                result.put("发送人次", String.valueOf(Integer.parseInt(result.getOrDefault("发送人次", "0")) + 1));
                result.put("发送人员", result.getOrDefault("发送人员", "") + "," + username);
                if (!flag) {
                    result.put("失败人次", String.valueOf(Integer.parseInt(result.getOrDefault("失败人次", "0")) + 1));
                    result.put("失败人员", result.getOrDefault("失败人员", "") + "," + username);
                }
            });
        }
        log.info("发送短信成功，相关发送信息为：{}", result);
        if (result.get("失败人次") != null) {
            //获取当前登录人
            IUser iUser = SecurityUtils.getCurrentUser();
            LocalDateTime time = LocalDateTime.now();
            SysOperateLog operateLog = new SysOperateLog();
            operateLog.setOperateFlag("MSG");
            operateLog.setBussinessKey(examCode);
            operateLog.setInterfaceParam(result.toString());
            operateLog.setOperateInterface("action/summary/createExamSmsWork");
            operateLog.setCreator(iUser.getUsername());
            operateLog.setCreatedTime(time);
            operateLog.setModifier(iUser.getUsername());
            operateLog.setModifiedTime(time);
            operateLogService.saveLog(operateLog);
        }
        return result;
    }
}

/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamUser;
import com.simbest.boot.exam.examOnline.repository.ExamUserRepository;
import com.simbest.boot.exam.examOnline.service.IExamUserService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleGroup;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.group.UumsSysGroupApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <strong>Title : ExamUserServiceImpl</strong><br>
 * <strong>Description : 试卷人员Service </strong><br>
 * <strong>Create on : 2020/10/13</strong><br>
 * <strong>Modify on : 2020/10/13</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Service
@Slf4j
public class ExamUserServiceImpl extends LogicService<ExamUser,String> implements IExamUserService {
    private ExamUserRepository repository;

    @Autowired
    private UumsSysGroupApi uumsSysGroupApi;

    @Autowired
    public ExamUserServiceImpl(ExamUserRepository repository){
        super(repository);
        this.repository=repository;

    }

    @Override
    public List<ExamUser> getExamUserByQuestionBankCode(String questionBankCode,String username) {
        return this.findAllNoPage(Specifications.<ExamUser>and()
                    .eq("questionBankCode", questionBankCode)
                    .eq("username", username)
                    .eq("enabled", true)
                    .build(), Sort.by(Sort.Direction.ASC, "examinationSort"));
    }

    @Override
    public ExamUser getExamUserByBankCode(String bankCode, String username) {
        return this.findOne(Specifications.<ExamUser>and()
                .eq("bankCode", bankCode)
                .eq("username", username)
                .eq("enabled", true)
                .build());
    }

    @Override
    public JsonResponse judgeUserWhetherHavePermission() {

        List<SimpleGroup> simpleGroupList = uumsSysGroupApi.findGroupByUsernameNoPage(Constants.APP_CODE);

        boolean flag=false;

        for (SimpleGroup simpleGroup : simpleGroupList) {
            String sid = simpleGroup.getSid();
            if(Constants.POSITION_GROUP_SID.equals(sid)){
                flag=true;
            }
        }
        return JsonResponse.success(flag);
    }
}

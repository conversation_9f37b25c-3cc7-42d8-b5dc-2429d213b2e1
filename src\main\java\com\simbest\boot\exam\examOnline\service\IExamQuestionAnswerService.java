/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service;/**
 * Created by KZH on 2019/10/8 15:12.
 */

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:12
 * @desc 题目正确答案
 **/
public interface IExamQuestionAnswerService extends ILogicService<ExamQuestionAnswer,String> {

    /**
     * 根据题目编码获取答案
     * @param questionCode
     * @return
     */
    List<ExamQuestionAnswer> findAllByQuestionCode(String questionCode);


    boolean findAllCorrect(String questionCode,String answerCode);

    boolean findAllCorrectAndAndIsCorrect(String questionCode,String isCorrect, String answerCode);

    JsonResponse findAllByQuestionCodePage(String questionCode, Pageable pageable);

    List<ExamQuestionAnswer> findAllByQuestionCodeAndCorrect(String questionCode);

    /**
     * 获取正确答案
     * <AUTHOR>
     * @param questionCode
     * @param isCorrect
     *
     * @return
     */
    List<String> findIsCorrectByExamCode (String questionCode,String isCorrect);


    /**
     * 根据examCode 查询本厂考试所有正确答案
     * @param examCode
     * @return
     */
    List<Map<String,Object>>  findAllCorrectByQuestionBankCode(String examCode);

    /**
     * 根据examCode 获取本厂考试所有正确答案
     */
    Map<String, Map<String, Object>> findMapCorrectByQuestionBankCode(String examCode);
}

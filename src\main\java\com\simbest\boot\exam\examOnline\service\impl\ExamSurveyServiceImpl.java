/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.*;
import com.simbest.boot.exam.examOnline.repository.ExamInfoRepository;
import com.simbest.boot.exam.examOnline.repository.ExamSurveyRepository;
import com.simbest.boot.exam.examOnline.repository.ExamTaskRepository;
import com.simbest.boot.exam.examOnline.service.*;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.util.BelongInfoTool;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.OperateLogTool;
import com.simbest.boot.security.IOrg;
import com.simbest.boot.security.IPosition;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.redis.RedisUtil;
import com.simbest.boot.util.security.SecurityUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExamSurveyServiceImpl extends LogicService<ExamSurvey, String> implements IExamSurveyService {

    private ExamSurveyRepository examSurveyRepository;

    @Autowired
    public ExamSurveyServiceImpl(ExamSurveyRepository examSurveyRepository) {
        super(examSurveyRepository);
        this.examSurveyRepository = examSurveyRepository;
    }
    @Autowired
    private IExamSurveyService examSurveyService;


    @Autowired
    private ISysOperateLogService operateLogService;


    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private ExamInfoRepository examInfoRepository;


    @Autowired
    private IExamInfoService examInfoService;

    @Autowired
    private IExamInfoDetailService examInfoDetailService;

    @Autowired
    @Lazy
    private IExamWorkService examWorkService;

    @Autowired
    private IExamSummaryService examSummaryService;
    /**
     *
     * 新增
     *
     * @param examSurvey
     * @return
     */
    @Override
    public JsonResponse createExamSurvey(ExamSurvey examSurvey) {


        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 修改
     *
     * @param examSurvey
     * @return
     */
    @Override
    public JsonResponse updateExamSurvey(ExamSurvey examSurvey) {
        examSurveyService.update(examSurvey);
        return null;
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public JsonResponse deleteExamSurvey(String id) {
        examSurveyService.deleteById(id);
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @Override
    public JsonResponse findByExamSurvey(String id) {
        ExamSurvey examSurvey = examSurveyService.findById(id);
        return JsonResponse.success(examSurvey);
    }

    /**
     * 查询用户是否办理过
     *
     * @param userName
     * @return
     */
    @Override
    public ExamSurvey findExamSurvey(String userName) {
        return examSurveyRepository.findExamSurvey(userName);
    }

    /**
     * 提交接口
     *
     * @param currentUserCode
     * @param source
     * @param examInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamInfo submitExamNew(String currentUserCode, String source, ExamSurvey examSurvey) {
        SysOperateLog operateLog = new SysOperateLog();
        ExamInfo result = null;
        String param1 = "/submitExam";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode + "examInfo:" + JacksonUtils.obj2json(examSurvey);
        operateLog.setInterfaceParam(params);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examInfo", "submitExam", operateLog);
        if (returnObj != null) {
            log.error("保存日志失败！");
            return new ExamInfo();
        }
        ExamSurvey examSurvey2=null;
        try {
            // ------------------------------------防重复提交 start-----------------------
            String key = "submitExam-" + examSurvey.getUserName();
            String value = "no-submit";
            if (Objects.equals(RedisUtil.get(key), value)) {
                throw new IllegalStateException("提交请求正在处理，无法重复提交！");
            }
            if (RedisUtil.setIfAbsent(key, value)) {
                RedisUtil.expire(key, 2, TimeUnit.SECONDS);
            }
                //数据能查询到，说明保存过，现在要保存最新的
                 examSurvey2 = examSurveyService.findExamSurvey(examSurvey.getUserName());
                if(null!=examSurvey2){
                    examSurvey.setId(examSurvey2.getId());
                    examSurvey2 =examSurveyService.update(examSurvey);
                }else{
                    BelongInfoTool.setBelongCompanyAndDepartment(examSurvey);
                    examSurvey.setPmInsType("G");
                    examSurvey.setExamCode("2025-yjdy");
                    examSurvey2 = examSurveyService.insert(examSurvey);
                }

            // ------------------------------------防重复提交 end  -----------------------
            /* 2、核销统一待办 */
            // 将考试编码转换为待办类型
            ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode("2025-yjdy");
            Assert.notNull(summaryInfo, "考试信息不存在!");
            String workType = summaryInfo.getWorkType();
            Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
            // 根据提交人和待办类型获取待办工作并进行核销操作
//        List<ExamWork> workInfo = examWorkService.findByUsernameAndWorkType(o.getPublishUsername(), workType);
            List<ExamWork> workInfo = examWorkService.findAllNoPage(
                    Specifications.<ExamWork>and()
                            .eq("transactorCode", examSurvey2.getUserName())
                            .eq("workType", workType)
                            .eq("examCode", "2025-yjdy")
                            .eq("enabled", true)
                            .build());
            for (ExamWork examWork : workInfo) {
                if (null != examWork) {
                    examWorkService.dealWith(source, currentUserCode, examWork.getId());
                    examSurvey2.setPmInsId(examWork.getPmInsId());
                    examSurveyService.update(examSurvey2);
                } else {
                    // 提交问卷操作时待办一定存在
                    log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", examSurvey2.getUserName(), workType);
                }
            }
        } catch (Exception e ) {
            Exceptions.printException(e);
            operateLog.setErrorMsg("系统保存异常");
        } finally {
            operateLogService.insert(operateLog);
        }
        /* 3、返回入库数据 */
        return result;
    }

    /**
     * @param pmInsId
     * @return
     */
    @Override
    public ExamSurvey findExamSurveyByPmInsId(String pmInsId) {
        return examSurveyRepository.findExamSurveyByPmInsId(pmInsId);
    }

    /**
     * 查询一遍接口
     *
     * @param workId
     * @return
     */
    @Override
    public ExamSurvey findJoinDetail(String workId) {
        ExamWork examWork=examWorkService.findById(workId);
        ExamSurvey examSurvey = examSurveyService.findExamSurveyByPmInsId(examWork.getPmInsId());
        return examSurvey;
    }
}

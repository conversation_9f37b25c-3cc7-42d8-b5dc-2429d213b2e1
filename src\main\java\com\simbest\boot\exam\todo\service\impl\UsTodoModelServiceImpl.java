package com.simbest.boot.exam.todo.service.impl;

import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.todo.model.UsTodoModel;
import com.simbest.boot.exam.todo.repository.UsTodoModelRepository;
import com.simbest.boot.exam.todo.service.IUsTodoModelService;
import com.simbest.boot.exam.todo.service.TodoBusOperatorService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.ToDoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static io.netty.util.ResourceLeakDetector.setEnabled;

/**
 * @用途: 统一待办
 * @作者：zsf
 * @时间: 2018/12/27
 */
@Slf4j
@Service
public class UsTodoModelServiceImpl extends LogicService<UsTodoModel,Long> implements IUsTodoModelService {



    private UsTodoModelRepository usTodoModelRepository;


    public static final Map<String,String> todoHtml2;


    static {
        Map<String,String> todoHtmlTmp = Maps.newConcurrentMap( );
        todoHtmlTmp.put("A","/html/template/examCommon.html");
        todoHtmlTmp.put("C", "/html/template/questionnaireNbxc.html");//内部巡察五套（支持单选、多选、判断）
        todoHtmlTmp.put("B", "/html/template/questionnaireDdzw.html");//河南公司“担当作为”满意度问卷调查
       // todoHtmlTmp.put("D", "/html/template/gcPublicExam.html");//关于2024年工程领域所有人(含通信和工建)能力摸底考试功能的开发需求
        todoHtmlTmp.put("D", "/html/template/zwjdPublicExam.html");//2024年度领导干部自我监督测评(第二期
//        todoHtmlTmp.put("H","/html/exammanage/questionnaireNew.html");
        todoHtmlTmp.put("H","/html/template/questionnaireCYZD.html");
        todoHtmlTmp.put("T","/html/exammanage/itPublicExam.html");
        todoHtmlTmp.put("O","/html/template/questionnaireNew.html");
        todoHtmlTmp.put("P","/html/exammanage/itcxdsPublicExam.html");//豫起奋发-简报派发办理
        todoHtmlTmp.put("Z","/oaWeb/examadmin/#/workOrder");//豫起奋发-简报派发办理
        todoHtmlTmp.put("Q","/html/template/questionnaireBQZM.html");//豫起奋发-简报派发办理
        todoHtmlTmp.put("R","/html/template/questionnaireXCB.html");//巡察办滑块验证


        todoHtml2 = todoHtmlTmp;
    }

    @Value( "${app.host.port}" )
    public String hostPost;

    @Value("${spring.profiles.active}")
    public String profilesActive;
    @Autowired
    public UsTodoModelServiceImpl(UsTodoModelRepository repository){
        super(repository);
        this.usTodoModelRepository = repository;
    }
    @Autowired
    private TodoBusOperatorService todoBusOperatorService;

    /**
     * 保存推送待办数据到本地
     * @param usTodoModel      待办对象
     * @return
     */
    @Override
    public UsTodoModel savaLocalTodoData(UsTodoModel usTodoModel){
        try {
            setEnabled(true);
            return usTodoModelRepository.save( usTodoModel );
        }catch ( Exception e ){
            log.debug( "UsTodoModelServiceImpl>>>>>>>openTodo>>>>>调用接口平台推送统一代办异常" + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 根据业务待办id获取推送统一待办记录
     * @param key 待办id
     * @return
     */
    @Override
    public UsTodoModel getTodoByKey(String key) {
        UsTodoModel model = null;
        try {
            if (StringUtils.isNotEmpty( key )){
                Specification<UsTodoModel> build = Specifications.<UsTodoModel>and()
                        .eq("businessKey",key)
                        .build();
                List<UsTodoModel> list = usTodoModelRepository.findAll(build);
                if ( list.size() > 0){
                    model = list.get(0);
                }
            }
        }catch ( Exception e ){
            Exceptions.printException( e );
        }
        return model;
    }

    @Override
    public boolean openTodo(ExamWork work) {
        boolean flag = false;
        log.debug( "UsTodoModelServiceImpl>>>>>>>pushToDO>>>>>调用接口平台推送统一代办开始");
        try {
            log.debug("work>>>>>>>>" + work.toString());
            String id = work.getId();
            String username = work.getTransactorCode();
            log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>当前类型【{}】",work.getWorkType());
            //代办回调的路径
            String oaHtmlUrl ="";
            String urlParams="";
            if ("Z".equals(work.getWorkType())){
                String url="";
                if ("uat".equals(profilesActive)){
                    url ="/examadmin/#/workOrder";
                }else {
                    url=   todoHtml2.get(work.getWorkType());
                }
                oaHtmlUrl = hostPost  +url ;
                urlParams = "?pmInsId="+work.getPmInsId()
                        +"&answerType=3&type=task&createYear=" + work.getCreateYear()
                        + "&id=" + id  + "&name=auditVal&appcode=exam&from=oa&examAppCode="+work.getExamAppCode()
                        +"&examCode="+work.getExamCode()
                        + "&clientId=" + Constants.APP_ID + "&srcapp=jtmoa&itemType=0&showFlag=false";
            }else {
                 oaHtmlUrl = hostPost + "/" + Constants.APP_CODE + todoHtml2.get(work.getWorkType());
                urlParams = "?type=task&createYear=" + work.getCreateYear()
                        + "&id=" + id  + "&name=auditVal&appcode=exam&from=oa&examAppCode="+work.getExamAppCode()
                        +"&examCode="+work.getExamCode()
                        + "&clientId=" + Constants.APP_ID + "&srcapp=jtmoa&itemType=0&showFlag=false";
            }

            //代办回调路径后面带的参数，即url ?后面的数据?type=task&createYear=2019&id=W531985537079181312&name=detailVal
            UsTodoModel usTodoModel = new UsTodoModel();
            usTodoModel.setBusinessKey( id );
            usTodoModel.setBusinessStatusId( id );
            usTodoModel.setProcessInstanceId(id);
            usTodoModel.setWorkItemId( id );
            usTodoModel.setProcessDefId( work.getWorkType() );
            usTodoModel.setCreator( work.getCreator());
            usTodoModel.setCreatedTime( work.getCreatedTime());
            usTodoModel.setModifiedTime( work.getModifiedTime());
            usTodoModel.setModifier( work.getModifier() );
            usTodoModel.setUserName( username );
            usTodoModel.setSender( username );
            usTodoModel.setTitle( work.getTitle() );
            usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
            usTodoModel.setOaHtmlUrl(oaHtmlUrl);
            usTodoModel.setUrlParams(urlParams);
            usTodoModel.setWorkFlag(true);
            usTodoModel.setEnabled(true);
            usTodoModel.setSendFlag(false);
            usTodoModel.setSendDate( LocalDateTime.now() );
            //先保存本地推送记录，然后再调用接口平台推送代办
            log.debug("UsTodoModelServiceImpl>>>insert>>>>>开始" + usTodoModel.toString());
            UsTodoModel resultModel = savaLocalTodoData(usTodoModel);
            if ( resultModel != null ){
                todoBusOperatorService.openTodo( usTodoModel );
                flag = true;
                log.debug("usTodoModelService>>>insert>>>>>" + resultModel.toString());
                log.debug("UsTodoModelServiceImpl>>>insert>>>>>结束");
            }else {
                log.debug("usTodoModelService>>>insert失败" + usTodoModel.toString());
                log.debug("UsTodoModelServiceImpl>>>insert>>>>>结束");
            }
        }catch ( Exception e ){
            flag = false;
            log.debug("usTodoModelService>>>>>>>>>>>>>>>>>>异常" );
            Exceptions.printException( e );
        }
        return flag;
    }

    @Override
    public boolean closeTodo(ExamWork work) {
        boolean flag = false;
        log.debug( "UsTodoModelServiceImpl>>>>>>>closeTodo>>>>>调用接口平台推送统一代办开始");
        try {
            log.debug( "work>>>>>>>>"+work.toString());
            /**该工单推送过统一待办消待办**/
            if ( Constants.SIGN_O.equals( work.getIsTodoFlag())){
                String id = work.getId();
                UsTodoModel usTodoModel = getTodoByKey(id);
                if ( usTodoModel != null ){
                    usTodoModel.setModifier( work.getTransactor() );
                    usTodoModel.setModifiedTime(LocalDateTime.now());
                    usTodoModel.setTypeStatus(ToDoEnum.close.getValue());
                }
                UsTodoModel resultModel = update( usTodoModel );
                if ( resultModel != null ){
                    flag = true;
                    todoBusOperatorService.closeTodo( usTodoModel );
                }
            }
        }catch ( Exception e ){
            log.debug( "UsTodoModelServiceImpl>>>>>>>closeTodo>>>>>调用接口平台推送统一代办异常"+ e.getMessage());
            Exceptions.printException( e );
        }
        log.debug( "UsTodoModelServiceImpl>>>>>>>closeTodo>>>>>调用接口平台推送统一代办结束");
        return flag;
    }

    @Override
    public UsTodoModel findAllTypeStatus(String username,String workType) {
        return usTodoModelRepository.findAllTypeStatus(username,workType);
    }


}

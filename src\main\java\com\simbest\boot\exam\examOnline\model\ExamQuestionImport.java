/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.model;/**
 * Created by KZH on 2019/10/8 10:22.
 */


import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2019-10-08 10:22
 * @desc 题目导入表
 **/
@Data
public class ExamQuestionImport {

    @ExcelVOAttribute(name = "题库编码", column = "A")
    private String questionBankCode;

    @ExcelVOAttribute(name = "题目名称", column = "B")
    private String questionName;

    @ExcelVOAttribute(name = "题目类型", column = "C")
    private String questionType;

    @ExcelVOAttribute(name = "题目分数", column = "D")
    private String questionScore;

    @ExcelVOAttribute(name = "答案A", column = "E")
    private String answerA;

    @ExcelVOAttribute(name = "答案B", column = "F")
    private String answerB;

    @ExcelVOAttribute(name = "答案C", column = "G")
    private String answerC;

    @ExcelVOAttribute(name = "答案D", column = "H")
    private String answerD;

    @ExcelVOAttribute(name = "答案E", column = "I")
    private String answerE;

    @ExcelVOAttribute(name = "答案F", column = "J")
    private String answerF;

    @ExcelVOAttribute(name = "答案G", column = "K")
    private String answerG;

    @ExcelVOAttribute(name = "答案H", column = "L")
    private String answerH;

    @ExcelVOAttribute(name = "答案I", column = "M")
    private String answerI;

    @ExcelVOAttribute(name = "答案J", column = "N")
    private String answerJ;

    @ExcelVOAttribute(name = "答案K", column = "O")
    private String answerK;

    @ExcelVOAttribute(name = "答案L", column = "P")
    private String answerL;

    @ExcelVOAttribute(name = "正确答案", column = "Q")
    private String answerIsTrue;

    @ExcelVOAttribute(name = "题目分组", column = "R")
    private String questionGroupName;

}

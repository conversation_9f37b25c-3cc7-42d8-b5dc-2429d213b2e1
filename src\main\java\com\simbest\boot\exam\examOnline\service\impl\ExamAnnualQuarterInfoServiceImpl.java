package com.simbest.boot.exam.examOnline.service.impl;

import com.simbest.boot.base.service.impl.SystemService;
import com.simbest.boot.exam.examOnline.model.ExamAnnualQuarterInfo;
import com.simbest.boot.exam.examOnline.repository.ExamAnnualQuarterInfoRepository;
import com.simbest.boot.exam.examOnline.service.IExamAnnualQuarterInfoService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ExamAnnualQuarterInfoServiceImpl extends SystemService<ExamAnnualQuarterInfo,String> implements IExamAnnualQuarterInfoService {
    private ExamAnnualQuarterInfoRepository repository;

    @Autowired
    public ExamAnnualQuarterInfoServiceImpl (ExamAnnualQuarterInfoRepository repository){
        super(repository);
        this.repository=repository;
    }

    /**
     * 保存考试的年度季度信息
     * @param
     */
    @Override
    public void saveAnnualQuarterInfo(String examCode) {


            ExamAnnualQuarterInfo examAnnualQuarterInfo = new ExamAnnualQuarterInfo();
            //获取当前年份
            String currYear = DateUtil.getCurrYear();
            //第四季度时已是下一年
            int i = Integer.parseInt(currYear);
            String nextYear = String.valueOf(i - 1);
        //获取当前月份
            String currMonth = DateUtil.getCurrSimpleMonth();
            //获取  String 当前日期字符串 2018-03-14  作为annualQuarterCode 考试年度季度信息编码
            String currentStr = DateUtil.getCurrentTimestamp();
            examAnnualQuarterInfo.setAnnualQuarterCode(examCode + currentStr);

            String annualQuarterInfo = "";
            //根据月份确定季度
            if (examCode.equals(Constants.EXAM_CODE_BRANCH_LY)) {
                switch (currMonth) {
                    case "01":
                    case "02":
                    case "03":
                        annualQuarterInfo = nextYear + Constants.CURR_YEAR + Constants.CURR_MONTH_FOR + Constants.EXAM_ANNUAL_QUARTER_LX;
                        break;
                    case "04":
                    case "05":
                    case "06":
                        annualQuarterInfo = currYear + Constants.CURR_YEAR + Constants.CURR_MONTH_ONE + Constants.EXAM_ANNUAL_QUARTER_LX;
                        break;
                    case "07":
                    case "08":
                    case "09":
                        annualQuarterInfo = currYear + Constants.CURR_YEAR + Constants.CURR_MONTH_TWO + Constants.EXAM_ANNUAL_QUARTER_LX;
                        break;
                    case "10":
                    case "11":
                    case "12":
                        annualQuarterInfo = currYear + Constants.CURR_YEAR + Constants.CURR_MONTH_THR + Constants.EXAM_ANNUAL_QUARTER_LX;
                        break;
                }
            }
            if (examCode.equals(Constants.EXAM_CODE_OFFICE_LY)) {
                switch (currMonth) {
                    case "01":
                    case "02":
                    case "03":
                        annualQuarterInfo = currYear + Constants.CURR_YEAR + Constants.CURR_MONTH_FOR + Constants.EXAM_ANNUAL_QUARTER_LJ;
                        break;
                    case "04":
                    case "05":
                    case "06":
                        annualQuarterInfo = currYear + Constants.CURR_YEAR + Constants.CURR_MONTH_ONE + Constants.EXAM_ANNUAL_QUARTER_LJ;
                        break;
                    case "07":
                    case "08":
                    case "09":
                        annualQuarterInfo = currYear + Constants.CURR_YEAR + Constants.CURR_MONTH_TWO + Constants.EXAM_ANNUAL_QUARTER_LJ;
                        break;
                    case "10":
                    case "11":
                    case "12":
                        annualQuarterInfo = currYear + Constants.CURR_YEAR + Constants.CURR_MONTH_THR + Constants.EXAM_ANNUAL_QUARTER_LJ;
                        break;
                }
            }
            examAnnualQuarterInfo.setAnnualQuarterInfo(annualQuarterInfo);
            //保存考试的年度季度信息
            ExamAnnualQuarterInfo save = repository.save(examAnnualQuarterInfo);
    }



    /**
     * 查询考试的年度季度信息
     * @param annualQuarterCode
     * @return
     */
    @Override
    public ExamAnnualQuarterInfo findAnnualQuarterInfo(String annualQuarterCode,String createdTime) {

        //当是待办中查询年度季度信息则查询最新年度季度信息，已办中时传入年度季度的编码查询对应考试年度季度信息
        if (createdTime==null){
          return   repository.findExamAnnualQuarterInfoByAnnualQuarterCode2("%"+annualQuarterCode+"%");
        }

        if (createdTime.equals("no")){
            return   repository.findExamAnnualQuarterInfoByAnnualQuarterCode2("%"+annualQuarterCode+"%");
        }

        String createdTime2 = createdTime.substring(0, 10);
        String condition= annualQuarterCode+createdTime2;
        ExamAnnualQuarterInfo examAnnualQuarterInfoByAnnualQuarterCode = repository.findExamAnnualQuarterInfoByAnnualQuarterCode("%" + condition + "%");
        return examAnnualQuarterInfoByAnnualQuarterCode;
    }

    /**
      *根据模糊的年度季度编码获取年度季度信息
      * <AUTHOR>
      * @date 2021/6/30
      */
    @Override
    public ExamAnnualQuarterInfo findAnnualQuarterInfo3(String annualQuarterCode) {
     return    repository.findExamAnnualQuarterInfoByAnnualQuarterCode(annualQuarterCode);
    }

    /**
     * 查询所有考试的年度信息
     * @return
     */
    @Override
    public List<ExamAnnualQuarterInfo> findAllAnnualQuarterInfo(String annualQuarterCode) {
        return repository.findAllExamAnnualQuarterInfoByAnnualQuarterCode("%" + annualQuarterCode +"%");
    }
}

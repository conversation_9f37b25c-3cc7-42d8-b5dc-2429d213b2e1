/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;/**
 * Created by KZH on 2019/10/8 15:14.
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestionAnswer;
import com.simbest.boot.exam.examOnline.repository.ExamQuestionAnswerRepository;
import com.simbest.boot.exam.examOnline.service.IExamQuestionAnswerService;
import com.simbest.boot.util.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.regexp.RE;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:14
 * @desc 题目正确答案
 **/
@Slf4j
@Service
public class ExamQuestionAnswerServiceImpl extends LogicService<ExamQuestionAnswer,String> implements IExamQuestionAnswerService {

    private ExamQuestionAnswerRepository examQuestionAnswerRepository;



    @Autowired
    public ExamQuestionAnswerServiceImpl(ExamQuestionAnswerRepository repository){
        super(repository);
        this.examQuestionAnswerRepository=repository;

    }

    /**
     * 根据题目编码获取答案
     * @param questionCode
     * @return
     */
    @Override
    public List<ExamQuestionAnswer> findAllByQuestionCode(String questionCode) {
        List<ExamQuestionAnswer> allByQuestionCode = examQuestionAnswerRepository.findAllByQuestionCode(questionCode);
        if(CollUtil.isNotEmpty(allByQuestionCode)){
            for (ExamQuestionAnswer examQuestionAnswer : allByQuestionCode) {
                List<ExamQuestionAnswer> allByParentId = examQuestionAnswerRepository.findAllByParentId(examQuestionAnswer.getId());
                examQuestionAnswer.setExamQuestionAnswers(allByParentId);
            }
        }
        return allByQuestionCode;
    }

    @Override
    public boolean findAllCorrect(String questionCode, String answerCode) {

        ExamQuestionAnswer allCorrect = examQuestionAnswerRepository.findAllCorrect(questionCode, answerCode);
        return allCorrect != null;
    }

    @Override
    public boolean findAllCorrectAndAndIsCorrect(String questionCode, String isCorrect, String answerCode) {
        ExamQuestionAnswer allCorrectAndAndIsCorrect = examQuestionAnswerRepository.findAllCorrectAndAndIsCorrect(questionCode, isCorrect,answerCode);
        return allCorrectAndAndIsCorrect != null;
    }


    @Override
    public JsonResponse findAllByQuestionCodePage(String questionCode, Pageable pageable) {
        return JsonResponse.success(examQuestionAnswerRepository.findAllByQuestionCodePage(questionCode,pageable));
    }

    @Override
    public List<ExamQuestionAnswer> findAllByQuestionCodeAndCorrect(String questionCode) {
        return this.findAllNoPage(Specifications.<ExamQuestionAnswer>and()
                .like("questionCode", "%"+questionCode+"%")
                .eq("isCorrect",true)
                .eq("enabled", true)
                .build());
    }

    /**
     * 获取正确答案
     *
     * @param questionCode
     * @param isCorrect
     * @return
     */
    @Override
    public List<String> findIsCorrectByExamCode(String questionCode, String isCorrect) {
        return examQuestionAnswerRepository.findIsCorrectByExamCode(questionCode,isCorrect);
    }

    @Override
    public  List<Map<String,Object>> findAllCorrectByQuestionBankCode(String examCode) {
        return examQuestionAnswerRepository.findAllCorrectByQuestionBankCode(examCode);
    }

    /**
     * 根据examCode 获取本厂考试所有正确答案
     *
     * @param examCode
     */
    @Override
    public Map<String, Map<String, Object>> findMapCorrectByQuestionBankCode(String examCode) {
        Map<String, Map<String, Object>> result = null;
        try {
            result = RedisUtil.getBean("EXAM_QUESTION_" + examCode, new TypeReference<Map<String, Map<String, Object>>>() {});
            if (result == null) {
                List<Map<String, Object>> list = examQuestionAnswerRepository.findAllCorrectByQuestionBankCode(examCode);
                if (CollectionUtil.isNotEmpty(list)) {
                    result = Maps.newHashMap();
                    for (Map<String, Object> map : list) {
                        result.put(map.get("ANSWECODE").toString(), map);
                    }
                    RedisUtil.setBean("EXAM_QUESTION_" + examCode, result);
                }
            }
        } catch (Exception e ) {
            Exceptions.printException(e);
        }
        return result;
    }
}

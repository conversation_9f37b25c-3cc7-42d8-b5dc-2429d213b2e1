package com.simbest.boot.exam.briefDistribution.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.PredicateBuilder;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.AuthoritiesConstants;
import com.simbest.boot.exam.briefDistribution.model.ApplyForm;
import com.simbest.boot.exam.briefDistribution.model.UserInfo;
import com.simbest.boot.exam.briefDistribution.repository.ApplyFormRepository;
import com.simbest.boot.exam.briefDistribution.repository.UserInfoRepository;
import com.simbest.boot.exam.briefDistribution.service.ApplyFormService;
import com.simbest.boot.exam.briefDistribution.service.ISyncCommonService;
import com.simbest.boot.exam.briefDistribution.service.UserInfoService;
import com.simbest.boot.exam.flow.model.SysTaskInfo;
import com.simbest.boot.exam.flow.service.ISysTaskInfoService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.util.PaginationHelp;
import com.simbest.boot.util.distribution.id.IdGenerator;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.util.http.client.HttpClient;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class UserInfoImpl extends LogicService<UserInfo, String> implements UserInfoService {


    private final UserInfoRepository repository;

    public UserInfoImpl(UserInfoRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    public List<UserInfo> findByBusinessId(String businessId) {
        return repository.findUserInfosByBusinessIdAndEnabledIsTrue(businessId);
    }
}

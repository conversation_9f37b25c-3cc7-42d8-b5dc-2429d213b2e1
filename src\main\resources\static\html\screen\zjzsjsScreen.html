<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>河南移动"赋能建功"2025年纪检知识竞赛</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: url('../../images/screen/bg.jpg') no-repeat center center;
            background-size: 100% 100%;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        .screen-container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 40px 60px;
            position: relative;
        }

        .title-section {
            text-align: center;
            margin-bottom: 40px;
            z-index: 10;
        }

        .main-title {
            font-size: 48px;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            margin-bottom: 10px;
            letter-spacing: 3px;
        }

        .sub-title {
            font-size: 36px;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            letter-spacing: 2px;
        }

        .ranking-container {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 248, 248, 0.9));
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 1400px;
            max-height: 70vh;
            overflow: hidden;
            position: relative;
        }

        .ranking-header {
            display: grid;
            grid-template-columns: 80px 200px 120px 120px 80px 200px 120px 120px;
            gap: 20px;
            padding: 15px 20px;
            background: linear-gradient(135deg, #d32f2f, #f44336);
            color: white;
            font-weight: bold;
            font-size: 18px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .ranking-content {
            max-height: 500px;
            overflow-y: auto;
            padding-right: 10px;
        }

        .ranking-row {
            display: grid;
            grid-template-columns: 80px 200px 120px 120px 80px 200px 120px 120px;
            gap: 20px;
            padding: 12px 20px;
            margin-bottom: 8px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-size: 16px;
            text-align: center;
            align-items: center;
        }

        .ranking-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .ranking-row:nth-child(odd) {
            background: #f8f9fa;
        }

        .rank-number {
            font-weight: bold;
            color: #333;
        }

        .rank-1 .rank-number {
            color: #FFD700;
            font-size: 20px;
        }

        .rank-2 .rank-number {
            color: #C0C0C0;
            font-size: 18px;
        }

        .rank-3 .rank-number {
            color: #CD7F32;
            font-size: 18px;
        }

        .user-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .score {
            font-weight: bold;
            color: #e74c3c;
        }

        .time {
            color: #7f8c8d;
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-size: 18px;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 40px;
            font-size: 18px;
            color: #e74c3c;
        }

        /* 滚动条样式 */
        .ranking-content::-webkit-scrollbar {
            width: 8px;
        }

        .ranking-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .ranking-content::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .ranking-content::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .ranking-row {
            animation: fadeInUp 0.6s ease forwards;
        }

        .ranking-row:nth-child(1) { animation-delay: 0.1s; }
        .ranking-row:nth-child(2) { animation-delay: 0.2s; }
        .ranking-row:nth-child(3) { animation-delay: 0.3s; }
        .ranking-row:nth-child(4) { animation-delay: 0.4s; }
        .ranking-row:nth-child(5) { animation-delay: 0.5s; }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .ranking-header,
            .ranking-row {
                grid-template-columns: 60px 150px 100px 100px 60px 150px 100px 100px;
                gap: 15px;
                font-size: 14px;
            }

            .main-title {
                font-size: 36px;
            }

            .sub-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="screen-container">
        <div class="ranking-container">
            <div class="ranking-header">
                <div>排名</div>
                <div>用户姓名</div>
                <div>得分</div>
                <div>用时</div>
                <div>排名</div>
                <div>用户姓名</div>
                <div>得分</div>
                <div>用时</div>
            </div>

            <div class="ranking-content" id="rankingContent">
                <div class="loading">正在加载排行榜数据...</div>
            </div>
        </div>
    </div>

    <script src="../../js/jquery.min.js"></script>
    <script>
        // 模拟排行榜数据
        const mockRankingData = [
            { rank: 1, name: "张伟", score: 100, time: "19分钟" },
            { rank: 2, name: "王芳", score: 100, time: "20分钟" },
            { rank: 3, name: "李强", score: 99, time: "18分钟" },
            { rank: 4, name: "刘波", score: 98, time: "17分钟" },
            { rank: 5, name: "陈静", score: 97, time: "16分钟" },
            { rank: 6, name: "杨帆", score: 96, time: "15分钟" },
            { rank: 7, name: "赵磊", score: 95, time: "19分钟" },
            { rank: 8, name: "黄娟", score: 94, time: "18分钟" },
            { rank: 9, name: "周勇", score: 93, time: "20分钟" },
            { rank: 10, name: "吴敏", score: 92, time: "20分钟" },
            { rank: 11, name: "徐涛", score: 91, time: "19分钟" },
            { rank: 12, name: "孙丽", score: 90, time: "19分钟" },
            { rank: 13, name: "朱秀英", score: 90, time: "20分钟" },
            { rank: 14, name: "马强", score: 89, time: "19分钟" },
            { rank: 15, name: "胡杰", score: 88, time: "18分钟" },
            { rank: 16, name: "林雪", score: 87, time: "17分钟" },
            { rank: 17, name: "郭洋", score: 86, time: "16分钟" },
            { rank: 18, name: "何明", score: 85, time: "15分钟" },
            { rank: 19, name: "高静", score: 84, time: "19分钟" },
            { rank: 20, name: "罗平", score: 83, time: "18分钟" },
            { rank: 21, name: "郑华", score: 82, time: "20分钟" },
            { rank: 22, name: "梁艳", score: 81, time: "19分钟" },
            { rank: 23, name: "谢刚", score: 81, time: "20分钟" },
            { rank: 24, name: "宋军", score: 81, time: "19分钟" }
        ];

        // 渲染排行榜
        function renderRanking(data) {
            const content = $('#rankingContent');
            content.empty();

            // 将数据分成两列显示
            const leftColumn = [];
            const rightColumn = [];

            for (let i = 0; i < data.length; i++) {
                if (i % 2 === 0) {
                    leftColumn.push(data[i]);
                } else {
                    rightColumn.push(data[i]);
                }
            }

            // 确保两列长度一致
            const maxLength = Math.max(leftColumn.length, rightColumn.length);

            for (let i = 0; i < maxLength; i++) {
                const leftItem = leftColumn[i] || { rank: '', name: '', score: '', time: '' };
                const rightItem = rightColumn[i] || { rank: '', name: '', score: '', time: '' };

                const rankClass1 = leftItem.rank <= 3 ? `rank-${leftItem.rank}` : '';
                const rankClass2 = rightItem.rank <= 3 ? `rank-${rightItem.rank}` : '';

                const row = `
                    <div class="ranking-row ${rankClass1}">
                        <div class="rank-number">${leftItem.rank}</div>
                        <div class="user-name">${leftItem.name}</div>
                        <div class="score">${leftItem.score}</div>
                        <div class="time">${leftItem.time}</div>
                        <div class="rank-number ${rankClass2}">${rightItem.rank}</div>
                        <div class="user-name">${rightItem.name}</div>
                        <div class="score">${rightItem.score}</div>
                        <div class="time">${rightItem.time}</div>
                    </div>
                `;
                content.append(row);
            }
        }

        // 获取排行榜数据的函数（当前使用模拟数据）
        function fetchRankingData() {
            // 当前使用模拟数据，后续可替换为真实接口调用
            // 真实接口示例：
            // $.ajax({
            //     url: '/action/usAnswerRecord/getSocreRanking',
            //     type: 'POST',
            //     data: {
            //         page: 1,
            //         size: 50,
            //         direction: 'DESC',
            //         properties: 'totalScore',
            //         source: 'PC'
            //     },
            //     success: function(response) {
            //         if (response.ret && response.data && response.data.page && response.data.page.content) {
            //             const rankingData = response.data.page.content.map((item, index) => ({
            //                 rank: item.rank || (index + 1),
            //                 name: item.ansewersTrueName || '未知',
            //                 score: item.totalScore || 0,
            //                 time: formatTime(item.timeSpentMinutes)
            //             }));
            //             renderRanking(rankingData);
            //         } else {
            //             $('#rankingContent').html('<div class="error">暂无排行榜数据</div>');
            //         }
            //     },
            //     error: function() {
            //         $('#rankingContent').html('<div class="error">加载排行榜数据失败</div>');
            //     }
            // });

            // 当前使用模拟数据
            setTimeout(() => {
                renderRanking(mockRankingData);
            }, 1000);
        }

        // 格式化时间显示
        function formatTime(timeSpentMinutes) {
            if (!timeSpentMinutes) return '0分钟';
            try {
                const minutes = parseInt(timeSpentMinutes);
                return minutes + '分钟';
            } catch (e) {
                return '0分钟';
            }
        }

        // 页面加载完成后获取数据
        $(document).ready(function() {
            fetchRankingData();

            // 每30秒刷新一次数据
            setInterval(fetchRankingData, 30000);
        });
    </script>
</body>
</html>
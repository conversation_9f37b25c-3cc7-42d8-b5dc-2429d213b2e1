package com.simbest.boot.exam.examOnline.web;

import cn.hutool.core.lang.Dict;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamSummary;
import com.simbest.boot.exam.examOnline.service.IExamSummaryService;
import com.simbest.boot.util.DateUtil;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 用途：考试信息模块--考试汇总controller
 * 作者：gy
 * 时间: 2021-02-01 10:56
 */
@Api(description = "ExamSummaryController", tags = {"考试信息模块-考试汇总信息控制器"})
@Slf4j
@RestController
@RequestMapping("/action/summary")
public class ExamSummaryController extends LogicController<ExamSummary, String> {
    private IExamSummaryService service;

    @Autowired
    public ExamSummaryController(IExamSummaryService service) {
        super(service);
        this.service = service;
    }

    @Value("${app.exam.mobile.examCode}")
    private String examCodeByMobile;

    @ApiOperation(value = "保存考试汇总信息", notes = "保存考试汇总信息")
    @PostMapping(value = {"/saveExamSummary", "/sso/saveExamSummary", "/api/saveExamSummary"})
    public JsonResponse saveExamSummary(@RequestBody ExamSummary o) {
        return JsonResponse.success(service.saveExamSummary(o), "操作成功!");
    }

    @ApiOperation(value = "获取考试汇总信息详情", notes = "获取考试汇总信息详情")
    @ApiImplicitParam(name = "id", value = "考试汇总信息外键", dataType = "String", paramType = "query")
    @PostMapping(value = {"/findExamSummaryInfo", "/sso/findExamSummaryInfo", "/api/findExamSummaryInfo"})
    public JsonResponse findExamSummaryInfo(@RequestParam String id) {
        return JsonResponse.success(service.findExamSummaryInfo(id));
    }

    @ApiOperation(value = "获取考试汇总信息详情", notes = "获取考试汇总信息详情")
    @ApiImplicitParam(name = "examCode", value = "考试汇总信息外键", dataType = "String", paramType = "query")
    @PostMapping(value = {"/findExamSummaryInfoByExamCode", "/sso/findExamSummaryInfoByExamCode", "/api/findExamSummaryInfoByExamCode"})
    public JsonResponse findExamSummaryInfoByExamCode(@RequestParam String examCode) {
        return JsonResponse.success(service.findExamSummaryInfoByExamCode(examCode));
    }


    @ApiOperation(value = "获取考试汇总列表信息", notes = "获取考试汇总列表信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "页容量", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "direction", value = "排序方向asc/desc", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "properties", value = "排序属性", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/findExamSummaryList", "/sso/findExamSummaryList", "/api/findExamSummaryList"})
    public JsonResponse findExamSummaryList(@RequestParam(required = false, defaultValue = "1") int page,
                                            @RequestParam(required = false, defaultValue = "10") int size,
                                            @RequestParam(required = false) String direction,
                                            @RequestParam(required = false) String properties,
                                            @RequestBody ExamSummary o) {
        return JsonResponse.success(service.findExamSummaryList(page, size, direction, properties, o));
    }

    @ApiOperation(value = "手机端问卷飘窗展示控制接口", notes = "手机端问卷飘窗展示控制接口（获取权重值最高的考试）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前用户编码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "请求所属来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "examCode", value = "考试编码", dataType = "String", paramType = "query")
    })
    @PostMapping(value = {"/findEffectiveExam", "/sso/findEffectiveExam", "/api/findEffectiveExam"})
    public JsonResponse findEffectiveExam(@RequestParam(required = false) String currentUserCode,
                                          @RequestParam(required = false) String source,
                                          @RequestParam(required = false) String examCode) {
        return JsonResponse.success(service.findEffectiveExam(currentUserCode, source));
    }


    @ApiOperation(value = "小程序查询当前人是否有权限", notes = "小程序查询当前人是否有权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前用户编码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "请求所属来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "examCode", value = "考试编码", dataType = "String", paramType = "query")
    })
    @PostMapping(value = {"/findEffectiveExamSms", "/sso/findEffectiveExamSms", "/api/findEffectiveExamSms"})
    public JsonResponse findEffectiveExamSms(@RequestParam(required = false) String currentUserCode,
                                           @RequestParam(defaultValue = "SMS") String source,
                                           @RequestParam String examCode) {
        return JsonResponse.success(service.findEffectiveExamSms(currentUserCode, source, examCode));
    }

    @ApiOperation(value = "手机办公安全校验接口", notes = "手机办公安全校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前用户编码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "请求所属来源", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "examCode", value = "考试编码", dataType = "String", paramType = "query")
    })
    @PostMapping(value = {"/findAccessToken", "/sso/findAccessToken", "/api/findAccessToken"})
    public JsonResponse findAccessToken(@RequestParam(required = false) String currentUserCode,
                                        @RequestParam(required = false) String source,
                                        @RequestParam String appcode) {
        return JsonResponse.success(service.findAccessToken(currentUserCode, source, appcode));
    }

    @ApiOperation(value = "根据考试编码推发待办", notes = "根据考试编码推发待办")
    @ApiImplicitParam(name = "examCode", value = "考试编码", dataType = "String", paramType = "query")
    @PostMapping(value = {"/createExamWork", "/sso/createExamWork", "/api/createExamWork"})
    public JsonResponse createExamWork(@RequestParam String examCode) {
        service.createExamWork(examCode);
        return JsonResponse.success(null, "操作成功");
    }

    @ApiOperation(value = "根据考试编码推送统一待办", notes = "根据考试编码推送统一待办")
    @ApiImplicitParam(name = "examCode", value = "考试编码", dataType = "String", paramType = "query")
    @PostMapping(value = {"/sendTodo", "/sso/sendTodo", "/api/sendTodo"})
    public JsonResponse sendTodo(@RequestParam String examCode) {
        // TODO 为和之前代码区分，不再将该方法放在统一待办统一短信模块
        service.sendTodo(examCode);
        return JsonResponse.success(null, "操作成功");
    }

    /**
     * 批量核销统一待办
     *
     * @param examCode 考试编号
     * @return JsonResponse 对象，表示处理结果
     */
    @PostMapping(value = {"/dealTodo", "/sso/dealTodo", "/api/dealTodo"})
    public JsonResponse dealTodo(@RequestParam String examCode) {
        service.dealTodo(examCode);
        return JsonResponse.success(null, "操作成功");
    }

    @ApiOperation(value = "根据考试编码发送催办短信", notes = "根据考试编码发送催办短信")
    @ApiImplicitParam(name = "examCode", value = "考试编码", dataType = "String", paramType = "query")
    @PostMapping(value = {"/sendUrgeSms", "/sso/sendUrgeSms", "/api/sendUrgeSms"})
    //public JsonResponse sendUrgeSms(@RequestParam String examCode) {
    public JsonResponse sendUrgeSms(@RequestParam Map<String, String> examCode) {
        // TODO 为和之前代码区分，不再将该方法放在统一待办统一短信模块
        String examCode1 = examCode.get("examCode");

        service.sendUrgeSms(examCode1);
        return JsonResponse.success(null, "操作成功");
    }

    @ApiOperation(value = "根据考试编码发送催办短信", notes = "根据考试编码发送催办短信")
    @PostMapping(value = {"/findLeftTree", "/sso/findLeftTree", "/api/findLeftTree"})
    public JsonResponse findLeftTree(@RequestParam(required = false) String examCode) {
        try {
            return JsonResponse.success(service.findLeftTree(examCode), null);
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.defaultErrorResponse();
        }
    }

    @ApiOperation(value = "根据待办类型查询考试信息", notes = "根据待办类型查询考试信息")
    @PostMapping(value = {"/findByWorkType", "/sso/findByWorkType", "/api/findByWorkType"})
    public JsonResponse findByWorkType(@RequestParam String workType) {
        try {
            return JsonResponse.success(service.findExamSummaryByWorkType(workType), null);
        } catch (Exception e) {
            Exceptions.printException(e);
            return JsonResponse.defaultErrorResponse();
        }
    }

    @ApiOperation(value = "根据考试编码推发短信待办", notes = "根据考试编码推发短信待办")
    @ApiImplicitParam(name = "examCode", value = "考试编码", dataType = "String", paramType = "query")
    @PostMapping(value = {"/createExamSmsWork", "/sso/createExamSmsWork", "/api/createExamSmsWork"})
    public JsonResponse createExamSmsWork(@RequestParam String examCode) {
        Map<String, String> result = service.createExamSmsWork(examCode);
        return JsonResponse.success(result, String.format("操作成功，发送人次:%s, 失败人次:%s", result.get("发送人次"), result.getOrDefault("失败人次","")));
    }

    @PostMapping(value = {"/checkValid" , "/checkValid/sso" , "/checkValid/api"})
    public JsonResponse checkValid(@RequestParam String examAppCode) {
        ExamSummary infoByExamAppCode = service.findInfoByExamAppCode(examAppCode);
        Assert.notNull(infoByExamAppCode , "考试配置信息异常！");
        Boolean hasValid = LocalDateTime.now().isAfter(infoByExamAppCode.getExamStartTime()) && LocalDateTime.now().isBefore(infoByExamAppCode.getExamEndTime());
        Dict dict = Dict.create().set("hasValid", hasValid);
        return JsonResponse.success(dict);

    }

    @ApiOperation(value = "心跳")
    @PostMapping(value = {"/checkHeath" , "/checkHeath/sso" , "/checkHeath/api"})
    public JsonResponse checkHeath(@RequestParam(defaultValue = "PC")String source ,
                                   @RequestParam(required = false) String currentUserCode) {
        log.warn("心跳正常：{}" , DateUtil.getCurrentTimestamp());
        return JsonResponse.defaultSuccessResponse();
    }

}

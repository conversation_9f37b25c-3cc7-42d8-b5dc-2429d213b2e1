package com.simbest.boot.exam.examOnline.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.exam.examOnline.model.ExamAnswerInfo;
import com.simbest.boot.exam.examOnline.model.ExamQuestionUser;
import com.simbest.boot.exam.examOnline.repository.ExamAnswerInfoRepository;
import com.simbest.boot.exam.examOnline.service.IExamAnswerInfoService;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
public class ExamAnswerInfoServiceImpl extends LogicService<ExamAnswerInfo, String> implements IExamAnswerInfoService {
    private ExamAnswerInfoRepository examAnswerInfoRepository;


    public ExamAnswerInfoServiceImpl(ExamAnswerInfoRepository examAnswerInfoRepository) {
        super(examAnswerInfoRepository);
        this.examAnswerInfoRepository = examAnswerInfoRepository;
    }

    @Override
    public ExamAnswerInfo findExamAnswerInfo(Map<String, Object> examMap) {
        ExamAnswerInfo examAnswerInfo=null;
        Specification<ExamAnswerInfo> build = Specifications.<ExamAnswerInfo>and()
                .eq("questionCode", MapUtil.getStr(examMap, "questionCode"))
                .eq("questionBankCode", MapUtil.getStr(examMap, "questionBankCode"))
                .eq("enabled", 1)
                .build();
        List<ExamAnswerInfo> allNoPage = findAllNoPage(build);
        if(!CollectionUtils.isEmpty(allNoPage)){
            examAnswerInfo = allNoPage.get(0);
        }
        return examAnswerInfo;
    }
}

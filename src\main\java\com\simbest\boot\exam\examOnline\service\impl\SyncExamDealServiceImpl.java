package com.simbest.boot.exam.examOnline.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.exam.examOnline.model.ExamInfo;
import com.simbest.boot.exam.examOnline.model.ExamInfoDetail;
import com.simbest.boot.exam.examOnline.service.*;
import com.simbest.boot.util.security.LoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: SyncExamDealServiceImpl
 * @description:
 * @author: ZHAOBO
 * @create: 2025-08-14 16:01
 */
@Service
@Slf4j
public class SyncExamDealServiceImpl implements ISyncExamDealService {

    @Autowired
    private IExamInfoDetailService examInfoDetailService;

    @Autowired
    private IExamInfoService examInfoService;

    @Autowired
    private IExamQuestionAnswerService examQuestionAnswerService;

    @Autowired
    private LoginUtils loginUtils;

    /**
     * 异步保存明细数据
     *
     * @param examInfo      明细数据
     */
    @Override
    public void syncSaveDetailInfo(ExamInfo examInfo) {
        loginUtils.adminLogin();
        Assert.notNull(examInfo , "考试数据信息不能为空！");
        Assert.notNull(examInfo.getExamCode() , "考试编码不能为空！");
        Assert.notNull(examInfo.getExamAppCode() , "考试应用编码不能为空！");
        Assert.notNull(examInfo.getExamRecord() , "考试记录不能为空！");
        Assert.notNull(examInfo.getExamAnswer() , "考试答案不能为空！");

        Map<String , Object> answerMap = null;
        /* 1、清空数据表现有考试明细数据 */
        Specification<ExamInfoDetail> spec = Specifications
                .<ExamInfoDetail>and()
                .eq("examCode", examInfo.getExamCode())
                .build();
        List<ExamInfoDetail> detailList = examInfoDetailService.findAllNoPage(spec);
        if (!detailList.isEmpty()) {
            examInfoDetailService.deleteAll(detailList);
        }

        Integer totalScore = 0;
        // 2、获取考试答题记录表信息
        Map<String,Map<String , Object>> correctMap= examQuestionAnswerService.findMapCorrectByQuestionBankCode(examInfo.getExamCode());
        /* 3、生成考试明细数据 */
        String examRecordStr = examInfo.getExamRecord();
        String examAnswerStr = examInfo.getExamAnswer();
        String[] examRecordSplit = examRecordStr.split(",");
        String[] examAnswerSplit = examAnswerStr.split(",",-1);
        for (int i = 0; i <= examRecordSplit.length - 1; i++) {
            try {
                String examRecord = examRecordSplit[i];
                String examAnswer = examAnswerSplit[i];
                ExamInfoDetail infoDetail = ExamInfoDetail
                        .builder()
                        .publishUsername(examInfo.getPublishUsername())
                        .publishTruename(examInfo.getPublishTruename())
                        .examInfoId(examInfo.getId())
                        .examCode(examInfo.getExamAppCode())
                        .examQuestionCode(examRecord)
                        .examAnswer(examAnswer)
                        .build();
                Map<String,Object> questionMap = correctMap.get(examRecord);
                if (questionMap != null) {
                    String trueanswer = MapUtil.getStr(questionMap , "TRUEANSWER");
                    String totalSocre = MapUtil.getStr(questionMap , "SCORE");
                    if (StrUtil.equals(examAnswer , trueanswer)) {
                        infoDetail.setScore(totalSocre);
                    } else {
                        infoDetail.setScore("0");
                    }
                }
                //获取考试的正确答案
                examInfoDetailService.insert(infoDetail);
                totalScore += Integer.parseInt(infoDetail.getScore());
            } catch (Exception e ) {
                Exceptions.printException(e);
            }
        }
        //更新分数
        examInfo.setScore(String.valueOf(totalScore));
        examInfoService.updateExamInfo(examInfo);
    }
}

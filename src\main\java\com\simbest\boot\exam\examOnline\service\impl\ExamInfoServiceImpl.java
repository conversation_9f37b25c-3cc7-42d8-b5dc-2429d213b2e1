/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.service.impl;/**
 * Created by KZH on 2019/10/8 15:14.
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.certificate.CertificateGenerator;
import com.simbest.boot.exam.certificate.path.model.CertificateTempPaths;
import com.simbest.boot.exam.certificate.service.ICertificateService;
import com.simbest.boot.exam.examOnline.model.*;
import com.simbest.boot.exam.examOnline.repository.ExamInfoRepository;
import com.simbest.boot.exam.examOnline.repository.ExamInfoSaveRepository;
import com.simbest.boot.exam.examOnline.service.*;
import com.simbest.boot.exam.examWork.model.ExamWork;
import com.simbest.boot.exam.examWork.service.IExamWorkService;
import com.simbest.boot.exam.util.Constants;
import com.simbest.boot.exam.util.FileTool;
import com.simbest.boot.exam.util.OperateLogTool;
import com.simbest.boot.exam.util.PageTool;
import com.simbest.boot.mq.service.SystemRabbitService;
import com.simbest.boot.security.IOrg;
import com.simbest.boot.security.IPosition;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.SimpleUser;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.encrypt.AesEncryptor;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.util.redis.RedisUtil;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:14
 * @desc 答题记录
 **/
@Slf4j
@Service
public class ExamInfoServiceImpl extends LogicService<ExamInfo, String> implements IExamInfoService {

    private ExamInfoRepository examInfoRepository;

    @Autowired
    private IExamQuestionAnswerService iExamQuestionAnswerService;

    @Autowired
    private IExamQuestionService iExamQuestionService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private IExamQuestionUserService iExamQuestionUserService;

    @Autowired
    private IExamUserService iExamUserService;

    @Autowired
    private ISysFileService iSysFileService;

    @Autowired
    private ICertificateService iCertificateService;

    @Autowired
    private IExamNumberService iExamNumberService;

    @Autowired
    @Lazy
    private IExamWorkService examWorkService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private IExamSummaryService examSummaryService;
    @Autowired
    private IExamAttributeService attributeService;
    @Autowired
    private AesEncryptor aesEncryptor;
    @Autowired
    private IExamInfoSaveService examInfoSaveService;
    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;
    @Autowired
    private ISysOperateLogService operateLogService;
    @Autowired
    private SystemRabbitService systemRabbitService;
    @Autowired
    private ExamInfoSaveRepository examInfoSaveRepository;
    @Autowired
    private ISyncExamDealService syncExamDealService;

    @Autowired
    public ExamInfoServiceImpl(ExamInfoRepository repository) {
        super(repository);
        this.examInfoRepository = repository;

    }

    @Override
    public JsonResponse saveExamInfo(ExamInfo examInfo) {

        String publishUsername = examInfo.getPublishUsername();
        String examAppCode = examInfo.getExamAppCode();
        ExamInfo allByPublishUsername = examInfoRepository.findAllByPublishUsername(publishUsername, examAppCode);
        if (allByPublishUsername == null || StrUtil.isEmpty(examInfo.getId())) {
            IUser currentUser = SecurityUtils.getCurrentUser();
            Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
            Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
            List<String> orgCode = Lists.newArrayList();
            List<String> orgName = Lists.newArrayList();
            List<String> positionName = Lists.newArrayList();
            for (IOrg iOrg : authOrgs) {
                orgCode.add(iOrg.getOrgCode());
                orgName.add(iOrg.getOrgName());
            }
            for (IPosition authPosition : authPositions) {
                positionName.add(authPosition.getPositionName());
            }
//            if(Constants.PORTAL.equals(examAppCode)
//                    ||Constants.HNJJWZ2.equals(examAppCode)
//                    ||Constants.HNJJWZ3.equals(examAppCode)){
//                examInfo.setIsFinishExam(true);
//            }
            examInfo.setExamCode(examAppCode);
            examInfo.setIsFinishExam(true);
            examInfo.setPublishTruename(currentUser.getTruename());
            examInfo.setIsMarkingExam(false);
            examInfo.setDepartmentCode(StringUtils.join(orgCode, ","));
            examInfo.setDepartmentName(StringUtils.join(orgName, ","));
            examInfo.setPositionName(StringUtils.join(positionName, ","));
            ExamInfo insert = this.insert(examInfo);
            return JsonResponse.success(insert);
        } else {
            ExamInfo update = this.update(examInfo);
            return JsonResponse.success(update);
        }
    }

    /**
     * 保存答题记录
     *
     * @param examInfo
     * @return
     */
    @Override
    public JsonResponse saveExamInfo(String currentUserCode, String source, ExamInfo examInfo) {

        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/constructExamLayout";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode;
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examInfo", "saveExamInfo", operateLog);
        if (returnObj != null) {
            return returnObj;
        }

        String publishUsername = examInfo.getPublishUsername();
        String examAppCode = examInfo.getExamAppCode();
        ExamInfo allByPublishUsername = examInfoRepository.findAllByPublishUsername(publishUsername, examAppCode);
        if (allByPublishUsername == null || StrUtil.isEmpty(examInfo.getId())) {
            IUser currentUser = SecurityUtils.getCurrentUser();
            Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
            Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
            List<String> orgCode = Lists.newArrayList();
            List<String> orgName = Lists.newArrayList();
            List<String> positionName = Lists.newArrayList();
            for (IOrg iOrg : authOrgs) {
                orgCode.add(iOrg.getOrgCode());
                orgName.add(iOrg.getOrgName());
            }
            for (IPosition authPosition : authPositions) {
                positionName.add(authPosition.getPositionName());
            }
//            if(Constants.PORTAL.equals(examAppCode)
//                    ||Constants.HNJJWZ2.equals(examAppCode)
//                    ||Constants.HNJJWZ3.equals(examAppCode)){
//                examInfo.setIsFinishExam(true);
//            }
            examInfo.setIsFinishExam(true);
            examInfo.setPublishTruename(currentUser.getTruename());
            examInfo.setIsMarkingExam(false);
            examInfo.setDepartmentCode(StringUtils.join(orgCode, ","));
            examInfo.setDepartmentName(StringUtils.join(orgName, ","));
            examInfo.setPositionName(StringUtils.join(positionName, ","));
            ExamInfo insert = this.insert(examInfo);
            return JsonResponse.success(insert);
        } else {
            ExamInfo update = this.update(examInfo);
            return JsonResponse.success(update);
        }

    }

    @Override
    public JsonResponse saveExamInfoNoUpdate(ExamInfo examInfo) {
        String publishUsername = examInfo.getPublishUsername();
        String examAppCode = examInfo.getExamAppCode();
        IUser currentUser = SecurityUtils.getCurrentUser();
        Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
        Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
        List<String> orgCode = Lists.newArrayList();
        List<String> orgName = Lists.newArrayList();
        List<String> positionName = Lists.newArrayList();
        for (IOrg iOrg : authOrgs) {
            orgCode.add(iOrg.getOrgCode());
            orgName.add(iOrg.getOrgName());
        }
        for (IPosition authPosition : authPositions) {
            positionName.add(authPosition.getPositionName());
        }
        if (Constants.PORTAL.equals(examAppCode)
                || Constants.HNJJWZ2.equals(examAppCode)
                || Constants.HNJJWZ3.equals(examAppCode)) {
            examInfo.setIsFinishExam(true);
        }
        examInfo.setPublishTruename(currentUser.getTruename());
        examInfo.setIsMarkingExam(false);
        examInfo.setDepartmentCode(StringUtils.join(orgCode, ","));
        examInfo.setDepartmentName(StringUtils.join(orgName, ","));
        examInfo.setPositionName(StringUtils.join(positionName, ","));
        ExamInfo insert = this.insert(examInfo);
        return JsonResponse.success(insert);
    }

    @Override
    public JsonResponse saveSpecialExamInfoNoUpdate(ExamInfo examInfo) {

        String examRecord = examInfo.getExamRecord();
        Assert.notNull(examRecord, "examRecord不可为空");
        String examAnswer = examInfo.getExamAnswer();
        Assert.notNull(examAnswer, "examAnswer不可为空");
        String examAppCode = examInfo.getExamAppCode();
        String[] examRecordList = examRecord.split(",");
        String[] examAnswerList = examAnswer.split(",");

        IUser currentUser = SecurityUtils.getCurrentUser();

        boolean returnUltimately = true;

        int score = 0;
        // 获取到全部题目
        List<ExamQuestionUser> examQuestionUserList = iExamQuestionUserService.getExamQuestionUserByBankCode(examRecordList, examAppCode, currentUser.getUsername());

        for (int i = 0; i <= examQuestionUserList.size() - 1; i++) {
            boolean ultimately = false;
            ExamQuestionUser str = examQuestionUserList.get(i);
            String str2 = examAnswerList[i];
            str.setUsernameAnswer(str2);
            iExamQuestionUserService.update(str);
            if (Constants.TRUE.equals(str2)) {//判断题校验
                ultimately = iExamQuestionAnswerService.findAllCorrectAndAndIsCorrect(str.getQuestionCode(), "1", "true");
            } else if (Constants.FALSE.equals(str2)) {
                ultimately = iExamQuestionAnswerService.findAllCorrectAndAndIsCorrect(str.getQuestionCode(), "1", "false");
            } else if (str2.contains("/")) {//多选判断
                boolean ultimately1 = true;
                String[] examAnswerList2 = str2.split("/");
                for (String str3 : examAnswerList2) {
                    boolean correct = iExamQuestionAnswerService.findAllCorrect(str.getQuestionCode(), str3);
                    if (!correct) {
                        ultimately1 = false;
                    }
                }
                ultimately = ultimately1;
            } else if (str2.length() == 1 && !"无".equals(str2)) {//单选判断

                ultimately = iExamQuestionAnswerService.findAllCorrect(str.getQuestionCode(), str2);
            } else if (str2.length() > 0) {
                ultimately = true;
            }
            if (ultimately) {
                score++;
            }


        }

        examInfo.setScore(String.valueOf(score));
        examInfo.setIsFinishExam(true);
        examInfo.setPublishTruename(currentUser.getTruename());
        examInfo.setIsMarkingExam(false);
        examInfo.setDepartmentCode(currentUser.getBelongDepartmentCode());

        //根据试卷判断是否生成证书文件
        if (Constants.HNJJWZ2.equals(examAppCode) && score >= 60) {//

            try {
                File file = CertificateGenerator.generateExternal(CertificateDetail.builder()
                        .truename(currentUser.getTruename())
                        .title("2020年纪检业务线上培训班的学习")
                        .number(RandomUtil.randomNumbers(20))
                        .date(DateUtil.formatChineseDate(DateUtil.date(), true))
                        .build());
                // 将生成的文件上传至服务器
                SysFile sysFile = iSysFileService.uploadLocalProcessFile(file, Constants.APP_CODE, null, Constants.HNJJWZ2);
                examInfo.setCertificateID(sysFile.getId());
            } catch (Exception e) {
                Exceptions.printException(e);
                log.error("证书生成失败saveSpecialExamInfoNoUpdate");
            }

        } else {
            log.info(currentUser.getUsername() + "考试成绩未达标");
        }

        saveExamInfo(examInfo);

        ExamUser examUserByBankCode = iExamUserService.getExamUserByBankCode(examAppCode, currentUser.getUsername());
        if (examUserByBankCode != null) {
            examUserByBankCode.setIsFinish(1);
            iExamUserService.update(examUserByBankCode);
        } else {
            log.warn(currentUser.getUsername() + "刷新试卷状态失败");
        }


        return JsonResponse.success(score);
    }

    @Override
    public JsonResponse unfinishedExam(ExamInfo examInfo) {
        String publishUsername = examInfo.getPublishUsername();
        String examAppCode = examInfo.getExamAppCode();
        Assert.notNull(publishUsername, "获取用户未完成试卷失败，username为空");
        ExamInfo allByPublishUsername = examInfoRepository.findAllByPublishUsername(publishUsername, examAppCode);
        return JsonResponse.success(allByPublishUsername);
    }

    @Override
    public ExamInfo unfinishedBackExam(String publishUsername, String examAppCode) {
        Assert.notNull(publishUsername, "获取用户未完成试卷失败，username为空");
        ExamInfo allByPublishUsername = examInfoRepository.findAllByPublishUsername(publishUsername, examAppCode);
        return allByPublishUsername;
    }

    /**
     * 判题
     *
     * @param examInfo
     * @return
     */
    @Override
    public JsonResponse judgeExam(ExamInfo examInfo) {
        String examRecord = examInfo.getExamRecord();
        Assert.notNull(examRecord, "examRecord不可为空");
        String examAnswer = examInfo.getExamAnswer();
        Assert.notNull(examAnswer, "examAnswer不可为空");
        String publishUsername = examInfo.getPublishUsername();//答题人姓名
        String examAppCode = examInfo.getExamAppCode();
        String residueTime = examInfo.getResidueTime();//剩余时间
        String[] examRecordList = examRecord.split(",");
        String[] examAnswerList = examAnswer.split(",");

        List<ExamInfo> examInfoList = Lists.newArrayList();

        boolean returnUltimately = true;

        for (int i = 0; i <= examRecordList.length - 1; i++) {
            ExamInfo examInfo1 = new ExamInfo();
            boolean ultimately = false;
            String str = examRecordList[i];
            String str2 = examAnswerList[i];

            if (Constants.TRUE.equals(str2)) {//判断题校验
                ultimately = iExamQuestionAnswerService.findAllCorrectAndAndIsCorrect(str, "1", "true");
            } else if (Constants.FALSE.equals(str2)) {
                ultimately = iExamQuestionAnswerService.findAllCorrectAndAndIsCorrect(str, "1", "false");
            } else if (str2.contains("/")) {//多选判断
                boolean ultimately1 = true;
                String[] examAnswerList2 = str2.split("/");
                for (String str3 : examAnswerList2) {
                    boolean correct = iExamQuestionAnswerService.findAllCorrect(str, str3);
                    if (!correct) {
                        ultimately1 = false;
                    }
                }
                ultimately = ultimately1;
            } else if (str2.length() == 1 && !"无".equals(str2)) {//单选判断

                ultimately = iExamQuestionAnswerService.findAllCorrect(str, str2);
            } else if (str2.length() > 0) {
                ultimately = true;
            }
            examInfo1.setExamRecord(str);
            examInfo1.setExamAnswer(String.valueOf(ultimately));
            examInfoList.add(examInfo1);
            if (!ultimately) {
                returnUltimately = false;
            }
        }

        if (returnUltimately) {
            /**获取到答题记录**/
            ExamInfo findExamInfo = unfinishedBackExam(publishUsername, examAppCode);
            findExamInfo.setIsFinishExam(true);
            saveExamInfo(findExamInfo);
            return JsonResponse.success("true");
        } else {
            ExamInfo examInfo2 = unfinishedBackExam(publishUsername, examAppCode);
            if (examInfo2 != null) {
                examInfo2.setExamRecord(examRecord);
                examInfo2.setExamAnswer(examAnswer);
                examInfo2.setResidueTime(residueTime);
                saveExamInfo(examInfo2);
            }
        }

        return JsonResponse.success(examInfoList);
    }

    @Override
    public JsonResponse judgeExamPowerBuilding(ExamInfo examInfo) {
        String examRecord = examInfo.getExamRecord();
        Assert.notNull(examRecord, "examRecord不可为空");
        String examAnswer = examInfo.getExamAnswer();
        Assert.notNull(examAnswer, "examAnswer不可为空");
        String publishUsername = examInfo.getPublishUsername();//答题人姓名
        String examAppCode = examInfo.getExamAppCode();
        String residueTime = examInfo.getResidueTime();//剩余时间
        String[] examRecordList = examRecord.split(",");
        String[] examAnswerList = examAnswer.split(",");

        int sumScore = 0;

        for (int i = 0; i <= examRecordList.length - 1; i++) {

            boolean ultimately = false;
            String str = examRecordList[i];
            String str2 = examAnswerList[i];
            ultimately = iExamQuestionAnswerService.findAllCorrect(str, str2);
            // 正确得分
            if (ultimately) {
                sumScore = sumScore + 10;
            }

        }
        examInfo.setScore(String.valueOf(sumScore));
        ExamInfo examInfo2 = unfinishedBackExam(publishUsername, examAppCode);
        if (examInfo2 != null) {
            examInfo2.setScore(String.valueOf(sumScore));
            examInfo2.setExamRecord(examRecord);
            examInfo2.setExamAnswer(examAnswer);
            examInfo2.setResidueTime(residueTime);
            saveExamInfo(examInfo2);
        } else {
            saveExamInfo(examInfo);
        }

        /* 核销统一待办 */
        // 将考试编码转换为待办类型
        log.info(examAppCode);
        ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(examAppCode);
        Assert.notNull(summaryInfo, "考试信息不存在!");
        String workType = summaryInfo.getWorkType();
        Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
        // 根据提交人和待办类型获取待办工作并进行核销操作
        List<ExamWork> workInfo = examWorkService.findByUsernameAndWorkType(examInfo.getPublishUsername(), workType);
        for (ExamWork examWork : workInfo) {
            if (null != examWork) {
                examWorkService.dealWith("PC", publishUsername, examWork.getId());
            } else {
                // 提交问卷操作时待办一定存在
                log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", examInfo.getPublishUsername(), workType);
            }
        }


        return JsonResponse.success(examInfo);
    }

    @Override
    public JsonResponse judgeExamAndCertificate(ExamInfo examInfo) {

        String examRecord = examInfo.getExamRecord();
        Assert.notNull(examRecord, "examRecord不可为空");
        String examAnswer = examInfo.getExamAnswer();
        Assert.notNull(examAnswer, "examAnswer不可为空");
        String examAppCode = examInfo.getExamAppCode();
        String[] examRecordList = examRecord.split(",");
        String[] examAnswerList = examAnswer.split(",");

        IUser currentUser = SecurityUtils.getCurrentUser();

        boolean returnUltimately = true;

        int score = 0;
        int score2 = 0;
        for (int i = 0; i <= examRecordList.length - 1; i++) {
            boolean ultimately = false;
            String str = examRecordList[i];
            String str2 = examAnswerList[i];

            if (Constants.TRUE.equals(str2)) {//判断题校验
                ultimately = iExamQuestionAnswerService.findAllCorrectAndAndIsCorrect(str, "1", "true");
            } else if (Constants.FALSE.equals(str2)) {
                ultimately = iExamQuestionAnswerService.findAllCorrectAndAndIsCorrect(str, "1", "false");
            } else if (str2.contains("/")) {//多选判断
                boolean ultimately1 = true;
                String[] examAnswerList2 = str2.split("/");
                for (String str3 : examAnswerList2) {
                    boolean correct = iExamQuestionAnswerService.findAllCorrect(str, str3);
                    if (!correct) {
                        ultimately1 = false;
                    }
                }
                ultimately = ultimately1;
            } else if (str2.length() == 1 && !"无".equals(str2)) {//单选判断

                ultimately = iExamQuestionAnswerService.findAllCorrect(str, str2);
            } else if (str2.length() > 0) {
                ultimately = true;
            }

            if (ultimately) {
                if (str.contains("A-008")) {
                    score++;
                }
                if (str.contains("A-009")) {
                    score2++;
                }

            }
        }

        score = score * 2;
        score2 = score2 * 3;
        score = score + score2;
        examInfo.setScore(String.valueOf(score));
        examInfo.setIsFinishExam(true);
        examInfo.setPublishTruename(currentUser.getTruename());
        examInfo.setIsMarkingExam(false);
        examInfo.setDepartmentCode(currentUser.getBelongDepartmentCode());

        //根据试卷判断是否生成证书文件
        if (Constants.HNJJWZ2.equals(examAppCode) && score >= 60) {//

            try {
                String randomNumbers = RandomUtil.randomNumbers(20);
                CertificateTempPaths certificateTempPaths = iCertificateService.generate(CertificateDetail.builder()
                        .truename(currentUser.getTruename())
                        .title("2020年纪检业务线上培训班")
                        .number(randomNumbers)
                        .date(DateUtil.formatChineseDate(DateUtil.date(), true))
                        .build());
                // 将生成的文件上传至服务器
                SysFile sysFile = iSysFileService.uploadLocalProcessFile(new File(certificateTempPaths.getTempImagePathName()), null, null, null);
                examInfo.setFileId(sysFile.getId());
                examInfo.setCertificateID(randomNumbers);

                // 删除生成证书使用的临时文件
//                if(certificateTempPaths.deleteTempFiles()){
//                    log.info("临时文件清除完成");
//                }
//                else {
//                    log.info("临时文件清除失败");
//                }
            } catch (Exception e) {
                Exceptions.printException(e);
                log.error("证书生成失败-judgeExamAndCertificate");
            }

        } else {
            log.info(currentUser.getUsername() + "考试成绩未达标:" + score);
        }

        saveExamInfo(examInfo);

        return JsonResponse.success(score);
    }

    /**
     * 阅卷
     *
     * @param publishUsername
     * @return
     */
    @Override
    public JsonResponse markingExam(String publishUsername) {

        /**获取未评测过的试卷**/
        ExamInfo examInfo = examInfoRepository.findAllByIsMarkingExam(publishUsername);
        Assert.notNull(examInfo, "获取未评测过的试卷为空，请检查是否完成答题");
        String examRecord = examInfo.getExamRecord();
        String examAnswer = examInfo.getExamAnswer();
        String[] examRecordList = examRecord.split(",");
        String[] examAnswerList = examAnswer.split(",");

        List<ExamQuestion> examQuestionList = Lists.newArrayList();
        for (int i = 0; i < examRecordList.length; i++) {
            String questionCode = examRecordList[i];
            ExamQuestion examQuestion = iExamQuestionService.findAllByQuestionCode(questionCode);
            String questionType = examQuestion.getQuestionType();
            if (Constants.SHORTANSWER.equals(questionType)) {
                List<ExamQuestionAnswer> allByQuestionCode = iExamQuestionAnswerService.findAllByQuestionCode(questionCode);
                examQuestion.setAnswerList(allByQuestionCode);
                examQuestion.setDoneAnswer(examAnswerList[i]);
                examQuestionList.add(examQuestion);
            }
        }

        return JsonResponse.success(examQuestionList);
    }

    /**
     * 获取未阅卷人员
     *
     * @param examInfo
     * @param pageable
     * @return
     */
    @Override
    public JsonResponse findAllByIsMarkingExam(ExamInfo examInfo, Pageable pageable) {

        //人员
        String publishUsername = examInfo.getPublishUsername();

        //部门名称
        String departmentName = examInfo.getDepartmentName();

        String examAppCode = examInfo.getExamAppCode();

        //未完成试卷
        boolean isMarkingExam = false;

        boolean isFinishExam = true;

        Specification<ExamInfo> specification = Specifications.<ExamInfo>and()
                .like(StringUtils.isNotEmpty(publishUsername), "publishUsername", "%" + publishUsername + "%")
                .like(StringUtils.isNotEmpty(departmentName), "departmentName", "%" + departmentName + "%")
                .eq(StringUtils.isNotEmpty(examAppCode), "examAppCode", examAppCode)
                .eq(isMarkingExam, "isMarkingExam", isMarkingExam)
                .eq(isFinishExam, "isFinishExam", isFinishExam)
                .build();

        Page<ExamInfo> all = this.findAll(specification, pageable);
        return JsonResponse.success(all);
    }

    @Override
    public boolean accomplishMarking(String publishUsername) {
        int i = examInfoRepository.accomplishMarking(publishUsername);
        if (i == 1) {
            return true;
        }
        return false;
    }

    /**
     * 手动初始化职务
     *
     * @return
     */
    @Override
    public JsonResponse manualInitPosition() {
        long starTime = System.currentTimeMillis();
        List<ExamInfo> examInfoList = this.findAllNoPage();
        for (ExamInfo examInfo : examInfoList) {
            String publishUsername = examInfo.getPublishUsername();
            List<Map<String, Object>> allPosition = findAllPosition(publishUsername);
            if (allPosition.size() == 1) {
                Map<String, Object> map = allPosition.get(0);
                String positionname = (String) map.get("POSITIONNAME");
                examInfo.setPositionName(positionname);
                this.update(examInfo);

            } else if (allPosition.size() != 0) {
                List<String> list = new ArrayList<>();
                for (Map<String, Object> map : allPosition) {
                    String positionname = (String) map.get("POSITIONNAME");
                    list.add(positionname);
                }
                examInfo.setPositionName(StringUtils.join(list, ","));
                this.update(examInfo);
            } else {
                examInfo.setPositionName("一般员工");
                this.update(examInfo);
            }
        }

        long endTime = System.currentTimeMillis();
        log.warn("共用时：*" + (starTime - endTime) + "ms");

        return null;
    }

    @Override
    public ExamInfo findByExamInfo(String publishUsername, String workType) {
        return examInfoRepository.findByExamInfo(publishUsername, workType);
    }

    private List<Map<String, Object>> findAllPosition(String username) {
        Map<String, Object> map = Maps.newHashMap();

        map.put("username", username);
        String sql = " select * from UUMS.V_USER_ORG_POSITION t where t.username=:username";
        List<Map<String, Object>> maps = customDynamicWhere.queryNamedParameterForList(sql, map);

        return maps;
    }

    @Override
    public JsonResponse getExamInfoByUsername(String username) {

        // username 为空查询全部

        if ("tuxia".equals(username)) {
            username = null;
        }

        List<ExamInfo> examinationSort = this.findAllNoPage(Specifications.<ExamInfo>and()
                .eq(StrUtil.isNotEmpty(username), "publishUsername", username)
                .eq("examAppCode", Constants.HNJJWZ2)
                .eq("enabled", true)
                .build(), Sort.by(Sort.Direction.DESC, "publishUsername"));

        examinationSort.forEach(examInfo -> {
            String fileId = examInfo.getFileId();
            if (StrUtil.isNotEmpty(fileId)) {
                SysFile sysFile = iSysFileService.findById(fileId);
                examInfo.setSysFile(sysFile);
            }

        });

        return JsonResponse.success(examinationSort);
    }

    /**
     * 保存试卷
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param str             提交考试数据表
     * @return 返回答题记录信息
     */
    @Override
    public ExamInfoSave saveExamSalt(String currentUserCode, String source, String str) {
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/saveExamSalt";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode + "str:" + str;
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examInfo", "saveExamSalt", operateLog);
        if (returnObj != null) {
            log.error("保存日志失败！");
            return null;
        }
        ExamInfoSave o;
        try {
            String decrypt = aesEncryptor.decrypt(str);
            o = JacksonUtils.json2obj(decrypt, ExamInfoSave.class);
        } catch (Exception e) {
            Exceptions.printException(e);
            throw new IllegalStateException("保存出错,数据不合法，联系管理员处理！");
        }
        return examInfoSaveService.saveExam(currentUserCode, source, o);
    }

    /**
     * 保存试卷
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param o               提交考试数据表
     * @return 返回答题记录信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamInfo saveExam(String currentUserCode, String source, ExamInfo o) {
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/saveExam";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode + "examInfo:" + o.toString();
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examInfo", "saveExam", operateLog);
        if (returnObj != null) {
            log.error("保存日志失败！");
            return null;
        }
        // todo 前端传值问题 暂时手动写死
        /*if (StrUtil.isEmpty(o.getExamAppCode()) || StrUtil.isEmpty(o.getExamCode())) {
            o.setExamAppCode("2023_CYZD");
            o.setExamCode("2023_CYZD");
        }*/

        // 解密时间戳
//        o.setTimestamp(Long.valueOf(aesEncryptor.decrypt(o.getSalt())));
        // 根据用户名和考试编码获取用户提交信息
        ExamInfo info = examInfoRepository.findAllByPublishUsername(o.getPublishUsername(), o.getExamAppCode());
        if (Objects.isNull(info)) {
            // 如果用户没有保存记录，则进行新增操作
            IUser currentUser = SecurityUtils.getCurrentUser();
            Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
            Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
            List<String> orgCode = Lists.newArrayList();
            List<String> orgName = Lists.newArrayList();
            List<String> positionName = Lists.newArrayList();
            for (IOrg iOrg : authOrgs) {
                orgCode.add(iOrg.getOrgCode());
                orgName.add(iOrg.getOrgName());
            }
            for (IPosition authPosition : authPositions) {
                positionName.add(authPosition.getPositionName());
            }
            o.setId(null);
            o.setPublishUsername(currentUser.getUsername());
            o.setPublishTruename(currentUser.getTruename());
            o.setDepartmentCode(StringUtils.join(orgCode, ","));
            o.setDepartmentName(StringUtils.join(orgName, ","));
            o.setPositionName(StringUtils.join(positionName, ","));
            o.setIsFinishExam(false);
            o.setIsMarkingExam(false);
            return this.insert(o);
        } else if (!info.getIsFinishExam()) {
            // 如果用户已有保存记录，且答题状态为未完成答题，则进行修改操作
            // 多终端判断
            if ((Objects.nonNull(o.getTimestamp()) && Objects.nonNull(info.getTimestamp())) && (o.getTimestamp() < info.getTimestamp())) {
                throw new IllegalStateException("已在另一终端打开, 将在5秒后关闭");
            }
            o.setId(info.getId());
            o.setIsFinishExam(false);
            o.setIsMarkingExam(false);
            return this.update(o);
        } else {
            throw new IllegalStateException("考试已结束，无法提交！");
        }
    }

    /**
     * 保存试卷
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param o               提交考试数据表
     * @return 返回答题记录信息
     */
    @Override
    public ExamInfo saveExam2(String currentUserCode, String source, ExamInfo o) {
        // todo 前端传值问题 暂时手动写死
       /* if (StrUtil.isEmpty(o.getExamAppCode()) || StrUtil.isEmpty(o.getExamCode())) {
            o.setExamAppCode("2023_CYZD");
            o.setExamCode("2023_CYZD");
        }*/

        // 根据用户名和考试编码获取用户提交信息
        ExamInfo info = examInfoRepository.findAllByPublishUsername(o.getPublishUsername(), o.getExamAppCode());
        if (null != info && !info.getIsFinishExam()) {
            // 如果用户已有保存记录，且答题状态为未完成答题，则进行修改操作
            o.setId(info.getId());
            return this.update(o);
        } else {
            // 如果用户没有保存记录，则进行新增操作
            IUser currentUser = SecurityUtils.getCurrentUser();
            Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
            Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
            List<String> orgCode = Lists.newArrayList();
            List<String> orgName = Lists.newArrayList();
            List<String> positionName = Lists.newArrayList();
            for (IOrg iOrg : authOrgs) {
                orgCode.add(iOrg.getOrgCode());
                orgName.add(iOrg.getOrgName());
            }
            for (IPosition authPosition : authPositions) {
                positionName.add(authPosition.getPositionName());
            }
            o.setId(null);
            o.setPublishUsername(currentUser.getUsername());
            o.setPublishTruename(currentUser.getTruename());
            o.setDepartmentCode(StringUtils.join(orgCode, ","));
            o.setDepartmentName(StringUtils.join(orgName, ","));
            o.setPositionName(StringUtils.join(positionName, ","));
            o.setIsFinishExam(false);
            o.setIsMarkingExam(false);
            return this.insert(o);
        }
    }

    /**
     * 提交试卷
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param str             提交考试数据表
     * @return 返回答题记录信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamInfo submitExamSalt(String currentUserCode, String source, String str) {
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/submitExamSalt";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode + "str:" + str;
        operateLog.setInterfaceParam(param1);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examInfo", "submitExamSalt", operateLog);
        if (returnObj != null) {
            log.error("保存日志失败！");
            return null;
        }
        ExamInfo o;
        try {
            String decrypt = aesEncryptor.decrypt(str);
            o = JacksonUtils.json2obj(decrypt, ExamInfo.class);
        } catch (Exception e) {
            Exceptions.printException(e);
            throw new IllegalStateException("保存出错,数据不合法，联系管理员处理！");
        }
        return this.submitExamSalt(currentUserCode, source, o);
    }

    /**
     * 提交试卷
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param o               提交考试数据表
     * @return 返回答题记录信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamInfo submitExamSalt(String currentUserCode, String source, ExamInfo o) {
        return this.submitExamSalt(currentUserCode, source, o, true);
    }

    /**
     * 提交试卷
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param o               提交考试数据表
     * @return 返回答题记录信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamInfo submitExamSalt(String currentUserCode, String source, ExamInfo o, boolean checkExamTime) {
        // 准备日志数据
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/submitExamSalt";
        String params = String.format(",source=%s,userCode=%s,form=%s,checkExamTime=%s", source, currentUserCode, o, checkExamTime);
        operateLog.setInterfaceParam(params);
        log.debug("提交请求 接口----------{}---------->{}", param2, o);
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "/action/examInfo", param2, operateLog);
        if (Objects.nonNull(returnObj)) throw new IllegalStateException("提交请求出错！");


        // ------------------------------------防重复提交 start-----------------------
        String key = "submitExam-" + o.getPublishUsername() + "-" + o.getExamAppCode();
        String value = "no-submit";
        if (Objects.equals(RedisUtil.get(key), value))
            throw new IllegalStateException("提交请求正在处理，无法重复提交！");
        if (RedisUtil.setIfAbsent(key, value))
            RedisUtil.expire(key, 2, TimeUnit.SECONDS);
        // ------------------------------------防重复提交 end  -----------------------

        //判断是否已过考试时间
        if (checkExamTime) {
            ExamSummary examSummaryByCode = examSummaryService.findExamSummaryByCode(o.getExamCode());
            LocalDateTime examEndTime = examSummaryByCode.getExamEndTime();
            LocalDateTime nowTime = LocalDateTime.now();
            boolean before = nowTime.isBefore(examEndTime);
//            Assert.isTrue(before, "考试时间已过！");
        }

        Optional<ExamInfo> byNew = Optional.ofNullable(examInfoRepository.findAllByNew(o.getPublishUsername(), o.getExamCode(), o.getExamAppCode()));
//        if (byNew.isPresent()) throw new IllegalStateException("考试已结束，无法提交！");
        if (byNew.isPresent()) return byNew.get();

        // 提交考试
        IUser currentUser = SecurityUtils.getCurrentUser();
        Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
        Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
        List<String> orgCode = Lists.newArrayList();
        List<String> orgName = Lists.newArrayList();
        List<String> positionName = Lists.newArrayList();
        for (IOrg iOrg : authOrgs) {
            orgCode.add(iOrg.getOrgCode());
            orgName.add(iOrg.getOrgName());
        }
        for (IPosition authPosition : authPositions) {
            positionName.add(authPosition.getPositionName());
        }
        o.setId(null);
        o.setPublishTruename(currentUser.getTruename());
        o.setPublishUsername(currentUser.getUsername());
        o.setDepartmentCode(StringUtils.join(orgCode, ","));
        o.setDepartmentName(StringUtils.join(orgName, ","));
        o.setPositionName(StringUtils.join(positionName, ","));
        o.setIsFinishExam(true);
        o.setIsMarkingExam(false);
        ExamInfo result = super.insert(o);


        // 核销统一待办
        // 将考试编码转换为待办类型
        ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(result.getExamCode());
        Assert.notNull(summaryInfo, "考试信息不存在!");
        String workType = summaryInfo.getWorkType();
        Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");

        List<ExamWork> workInfo = examWorkService.findAllNoPage(Specifications.<ExamWork>and()
                .eq("transactorCode", o.getPublishUsername())
                .eq("workType", workType)
                .eq("examCode", o.getExamCode())
                .eq("enabled", true)
                .build());
        for (ExamWork examWork : workInfo) {
            if (null != examWork) {
                examWorkService.dealWith(source, currentUserCode, examWork.getId());
            } else {
                // 提交问卷操作时待办一定存在
                log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", o.getPublishUsername(), workType);
            }
        }

        operateLogService.saveLog(operateLog);
        systemRabbitService.operateLogSend(operateLog);
        return result;
    }

    @Override
    public ExamInfo submitExam1(String currentUserCode, String source, ExamInfo o) {

        // 提交考试
        IUser currentUser = SecurityUtils.getCurrentUser();
        Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
        Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
        List<String> orgCode = Lists.newArrayList();
        List<String> orgName = Lists.newArrayList();
        List<String> positionName = Lists.newArrayList();
        for (IOrg iOrg : authOrgs) {
            orgCode.add(iOrg.getOrgCode());
            orgName.add(iOrg.getOrgName());
        }
        for (IPosition authPosition : authPositions) {
            positionName.add(authPosition.getPositionName());
        }
        o.setId(null);
        o.setPublishTruename(currentUser.getTruename());
        o.setPublishUsername(currentUser.getUsername());
        o.setDepartmentCode(StringUtils.join(orgCode, ","));
        o.setDepartmentName(StringUtils.join(orgName, ","));
        o.setPositionName(StringUtils.join(positionName, ","));
        o.setIsFinishExam(true);
        o.setIsMarkingExam(false);
        ExamInfo result = super.insert(o);

        // 核销统一待办
        // 将考试编码转换为待办类型
        ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(result.getExamCode());
        Assert.notNull(summaryInfo, "考试信息不存在!");
        String workType = summaryInfo.getWorkType();
        Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");

        List<ExamWork> workInfo = examWorkService.findAllNoPage(Specifications.<ExamWork>and()
                .eq("transactorCode", o.getPublishUsername())
                .eq("workType", workType)
                .eq("examCode", o.getExamCode())
                .eq("enabled", true)
                .build());
        for (ExamWork examWork : workInfo) {
            if (null != examWork) {
                examWorkService.dealWith(source, currentUserCode, examWork.getId());
            } else {
                // 提交问卷操作时待办一定存在
                log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", o.getPublishUsername(), workType);
            }
        }

        return result;
    }

    /**
     * 提交试卷
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param o               提交考试数据表
     * @return 返回答题记录信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamInfo submitExam(String currentUserCode, String source, ExamInfo o) {
        SysOperateLog operateLog = new SysOperateLog();
        String param1 = "/submitExam";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode + "examInfo:" + o.toString();
        operateLog.setInterfaceParam(params);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examInfo", "submitExam", operateLog);
        if (returnObj != null) {
            log.error("保存日志失败！");
            return new ExamInfo();
        }
        // ------------------------------------防重复提交 start-----------------------
        String key = "submitExam-" + o.getPublishUsername();
        String value = "no-submit";
        if (Objects.equals(RedisUtil.get(key), value)) {
            throw new IllegalStateException("提交请求正在处理，无法重复提交！");
        }
        if (RedisUtil.setIfAbsent(key, value)) {
            RedisUtil.expire(key, 2, TimeUnit.SECONDS);
        }
        //多选题答题信息校验
        this.checkAnswerNum(o);

        // ------------------------------------防重复提交 end  -----------------------
        ExamInfo result;
        /* 1、保存问卷表单信息 */
        // 获取问卷提交人的问卷提交记录
        //判断是否已过考试时间
//        ExamSummary examSummaryByCode = examSummaryService.findExamSummaryByCode(o.getExamCode());
//        LocalDateTime examEndTime = examSummaryByCode.getExamEndTime();
//        LocalDateTime nowTime = LocalDateTime.now();
//        boolean before = nowTime.isBefore(examEndTime);
//        Assert.isTrue(before, "考试时间已过！");
        ExamInfo info = examInfoRepository.findAllByPublishUsername(o.getPublishUsername(), o.getExamAppCode());
        // 如果提交人未提交过，需要新增问卷提交记录信息
        if (info == null) {
            IUser currentUser = SecurityUtils.getCurrentUser();
            o.setBelongCompanyTypeDictValue(currentUser.getBelongCompanyTypeDictValue());
            o.setBelongCompanyCode(currentUser.getBelongCompanyCode());
            o.setBelongCompanyName(currentUser.getBelongCompanyName());
            o.setBelongDepartmentCode(currentUser.getBelongDepartmentCode());
            o.setBelongDepartmentName(currentUser.getBelongDepartmentName());
            o.setBelongOrgCode(currentUser.getBelongOrgCode());
            o.setBelongOrgName(currentUser.getBelongOrgName());
            //县公司保存所属市公司
            if (StrUtil.equals(currentUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE)) {
                o.setBelongCompanyCode(currentUser.getBelongCompanyCodeParent());
                o.setBelongCompanyName(currentUser.getBelongCompanyNameParent());
                o.setBelongDepartmentCode(currentUser.getBelongCompanyCode());
                o.setBelongDepartmentName(currentUser.getBelongCompanyName());
            }
            Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
            Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
            List<String> orgCode = Lists.newArrayList();
            List<String> orgName = Lists.newArrayList();
            List<String> positionName = Lists.newArrayList();
            for (IOrg iOrg : authOrgs) {
                orgCode.add(iOrg.getOrgCode());
                orgName.add(iOrg.getOrgName());
            }
            for (IPosition authPosition : authPositions) {
                positionName.add(authPosition.getPositionName());
            }
            o.setId(null);
            o.setPublishTruename(currentUser.getTruename());
            o.setPublishUsername(currentUser.getUsername());
            o.setDepartmentCode(StringUtils.join(orgCode, ","));
            o.setDepartmentName(StringUtils.join(orgName, ","));
            o.setPositionName(StringUtils.join(positionName, ","));
            o.setIsFinishExam(true);
            o.setIsMarkingExam(false);
            result = super.insert(o);
        } else if (!info.getIsFinishExam()) {
            // 如果提交人提交过问卷，则将问卷提交记录状态置为已答卷
            o.setId(info.getId());
            o.setIsFinishExam(true);
            result = super.update(o);
        } else {
            throw new IllegalStateException("考试已结束，无法提交！");
        }

        /* 2、核销统一待办 */
        // 将考试编码转换为待办类型
        ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(result.getExamCode());
        Assert.notNull(summaryInfo, "考试信息不存在!");

        //判断是否开启明细数据保存
        if ( null !=summaryInfo.getSaveDetailInfo() && summaryInfo.getSaveDetailInfo()) {
            syncExamDealService.syncSaveDetailInfo(result);
        }

        String workType = summaryInfo.getWorkType();
        Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
        // 根据提交人和待办类型获取待办工作并进行核销操作
//        List<ExamWork> workInfo = examWorkService.findByUsernameAndWorkType(o.getPublishUsername(), workType);
        List<ExamWork> workInfo = examWorkService.findAllNoPage(
                Specifications.<ExamWork>and()
                        .eq("transactorCode", o.getPublishUsername())
                        .eq("workType", workType)
                        .eq("examCode", o.getExamCode())
                        .eq("enabled", true)
                        .build());
        for (ExamWork examWork : workInfo) {
            if (null != examWork) {
                examWorkService.dealWith(source, currentUserCode, examWork.getId());
            } else {
                // 提交问卷操作时待办一定存在
                log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", o.getPublishUsername(), workType);
            }
        }
        /* 3、返回入库数据 */
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamInfo submitExamNew(String currentUserCode, String source, ExamInfo o) {
        SysOperateLog operateLog = new SysOperateLog();
        ExamInfo result = null;
        String param1 = "/submitExam";
        String params = "source=" + source + ",currentUserCode=" + currentUserCode + "examInfo:" + JacksonUtils.obj2json(o);
        operateLog.setInterfaceParam(params);
        /**判断来源记录日志**/
        JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, "action/examInfo", "submitExam", operateLog);
        if (returnObj != null) {
            log.error("保存日志失败！");
            return new ExamInfo();
        }
        try {
            // ------------------------------------防重复提交 start-----------------------
            String key = "submitExam-" + o.getPublishUsername();
            String value = "no-submit";
            if (Objects.equals(RedisUtil.get(key), value)) {
                throw new IllegalStateException("提交请求正在处理，无法重复提交！");
            }
            if (RedisUtil.setIfAbsent(key, value)) {
                RedisUtil.expire(key, 2, TimeUnit.SECONDS);
            }
            // ------------------------------------防重复提交 end  -----------------------

            //多选题答题信息校验
            //this.checkAnswerNum(o);
            ExamInfo info = examInfoRepository.findAllByPublishUsername(o.getPublishUsername(), o.getExamAppCode());
            //合并数据信息
            List<Map<String, Object>> allList = Lists.newArrayList();
            allList.addAll(o.getSingleData());
            allList.addAll(o.getMultipleData());
            allList.addAll(o.getJudgeData());
            allList.addAll(o.getFillingData());
            allList.addAll(o.getShortData());

            // 如果提交人未提交过，需要新增问卷提交记录信息
            if (info == null) {
                IUser currentUser = SecurityUtils.getCurrentUser();
                o.setBelongCompanyTypeDictValue(currentUser.getBelongCompanyTypeDictValue());
                o.setBelongCompanyCode(currentUser.getBelongCompanyCode());
                o.setBelongCompanyName(currentUser.getBelongCompanyName());
                o.setBelongDepartmentCode(currentUser.getBelongDepartmentCode());
                o.setBelongDepartmentName(currentUser.getBelongDepartmentName());
                o.setBelongOrgCode(currentUser.getBelongOrgCode());
                o.setBelongOrgName(currentUser.getBelongOrgName());
                //县公司保存所属市公司
                if (StrUtil.equals(currentUser.getBelongCompanyTypeDictValue(), Constants.COUNTY_CODE)) {
                    o.setBelongCompanyCode(currentUser.getBelongCompanyCodeParent());
                    o.setBelongCompanyName(currentUser.getBelongCompanyNameParent());
                    o.setBelongDepartmentCode(currentUser.getBelongCompanyCode());
                    o.setBelongDepartmentName(currentUser.getBelongCompanyName());
                }
                Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
                Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
                List<String> orgCode = Lists.newArrayList();
                List<String> orgName = Lists.newArrayList();
                List<String> positionName = Lists.newArrayList();
                for (IOrg iOrg : authOrgs) {
                    orgCode.add(iOrg.getOrgCode());
                    orgName.add(iOrg.getOrgName());
                }
                for (IPosition authPosition : authPositions) {
                    positionName.add(authPosition.getPositionName());
                }
                o.setId(null);
                o.setPublishTruename(currentUser.getTruename());
                o.setPublishUsername(currentUser.getUsername());
                o.setDepartmentCode(StringUtils.join(orgCode, ","));
                o.setDepartmentName(StringUtils.join(orgName, ","));
                o.setPositionName(StringUtils.join(positionName, ","));
                o.setIsFinishExam(true);
                o.setIsMarkingExam(false);
                String questionCodes = allList.stream().map(answer -> MapUtil.getStr(answer, "questionCode")).collect(Collectors.joining(","));
                String examAnswers = allList.stream().map(answer -> MapUtil.getStr(answer, "examAnswer").replace(",", "，")).collect(Collectors.joining(","));
                o.setExamAnswer(examAnswers);
                o.setExamRecord(questionCodes);
                result = super.insert(o);
                //异步保存数据信息
                syncExamDealService.syncSaveDetailInfo(result);
            } else if (!info.getIsFinishExam()) {
                // 如果提交人提交过问卷，则将问卷提交记录状态置为已答卷
                o.setId(info.getId());
                o.setIsFinishExam(true);
                String questionCodes = allList.stream().map(answer -> MapUtil.getStr(answer, "questionCode")).collect(Collectors.joining(","));
                String examAnswers = allList.stream().map(answer -> MapUtil.getStr(answer, "examAnswer").replace(",", "，")).collect(Collectors.joining(","));
                o.setExamAnswer(examAnswers);
                o.setExamRecord(questionCodes);
                result = super.update(o);
                //异步保存单条数据信息
                syncExamDealService.syncSaveDetailInfo(result);
            } else {
                throw new IllegalStateException("考试已结束，无法提交！");
            }
            /* 2、核销统一待办 */
            // 将考试编码转换为待办类型
            ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(result.getExamCode());
            Assert.notNull(summaryInfo, "考试信息不存在!");
            String workType = summaryInfo.getWorkType();
            Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
            // 根据提交人和待办类型获取待办工作并进行核销操作
//        List<ExamWork> workInfo = examWorkService.findByUsernameAndWorkType(o.getPublishUsername(), workType);
            List<ExamWork> workInfo = examWorkService.findAllNoPage(
                    Specifications.<ExamWork>and()
                            .eq("transactorCode", o.getPublishUsername())
                            .eq("workType", workType)
                            .eq("examCode", o.getExamCode())
                            .eq("enabled", true)
                            .build());
            for (ExamWork examWork : workInfo) {
                if (null != examWork) {
                    examWorkService.dealWith(source, currentUserCode, examWork.getId());
                } else {
                    // 提交问卷操作时待办一定存在
                    log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", o.getPublishUsername(), workType);
                }
            }
        } catch (Exception e ) {
            Exceptions.printException(e);
            operateLog.setErrorMsg("系统保存异常");
        } finally {
            operateLogService.insert(operateLog);
        }
        /* 3、返回入库数据 */
        return result;
    }


    /**
     *  校验数据提交信息是否一致，且多选题是否校验答题个数
     * @param o
     */
    private void checkAnswerNum(ExamInfo o) {
        Assert.notNull(o , "提交数据不能为空！");
        String examRecord = o.getExamRecord();
        String examAnswer = o.getExamAnswer();
        Assert.notNull(examRecord , "题目编码不能为空！");
        Assert.notNull(examAnswer, "提交答案不能为空！");
        int examRecordSize=0;
        int examAnswerSize=0;
        for (int i = 0; i < examRecord.length(); i++) {
            if (examRecord.charAt(i) == ','){
                examRecordSize++;
            }
        }
        for (int i = 0; i < examAnswer.length(); i++) {
            if (examAnswer.charAt(i) == ','){
                examAnswerSize++;
            }
        }
        String[] examRecordArray = examRecord.split(",");
        String[] examAnswerArray = examAnswer.split(",");
        Assert.isTrue(examRecordSize==examAnswerSize , "题目数量与答案数量不一致！");

        Assert.notNull(o.getExamAppCode() , "试卷编码不能为空！");
        //查询考试配置信息，确认校验类型
        ExamAttribute examAttribute = attributeService.getExamAttributeByExamAppCode(o.getExamAppCode());
        Assert.notNull(examAttribute , "试卷配置信息获取失败！");
        //进入多选题答题数量校验
        if (null != examAttribute.getCheckType() &&  examAttribute.getCheckType() == 1) {
            List<ExamQuestion> examQuestions = iExamQuestionService.findAllByQuestionBankCode(Arrays.asList(examRecordArray));
            Assert.notEmpty(examQuestions , "获取题目信息失败！") ;
            Map<String, ExamQuestion> map = Maps.newHashMap();
            examQuestions.stream().forEach(examQuestion -> {
                map.put(examQuestion.getQuestionCode() , examQuestion);
            });
            //处理提交答案，方便后续比对
            for (int i = 0; i < examRecordArray.length ; i ++) {
                ExamQuestion examQuestion = map.get(examRecordArray[i]);
                Assert.notNull(examQuestion , "第" + (i+1) +  "题目信息查询失败！");
                if (StrUtil.equals(examQuestion.getQuestionType() , "more" ) && null != examQuestion.getMaxChooseNum()) {
                    String[] answerArray = examAnswerArray[i].split("/");
                    if (answerArray.length > examQuestion.getMaxChooseNum()) {
                        throw new RuntimeException("第" + (i+1) + "题答案个数超出配置，请按照题目规则进行答题。");
                    }
                }
            }
        }
    }
    /**
     * 判断/的个数
     *
     * @param str
     * @return
     */
    public int countSlashes(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (int i = 0; i < str.length(); i++) {
            if (str.charAt(i) == '/') {
                count++;
            }
        }
        return count;
    }


    /**
     * 截取指定符号之前数据
     *
     * @param str
     * @return
     */
    public String extractBeforeColon(String str) {
        if (str == null || str.isEmpty() || str.indexOf(':') == -1) {
            return str;
        }

        int index = str.indexOf(':');
        return str.substring(0, index);
    }

    /**
     * 提交试卷(嵌入式廉洁风险责任人员测试)
     *
     * @param currentUserCode 当前人编码
     * @param source          请求来源
     * @param o               提交考试数据表
     * @return 返回答题记录信息
     */
    @Override
    public ExamInfo submitExamHonestRisk(String currentUserCode, String source, ExamInfo o) {
        ExamInfo result;
        ExamNumber examNumber = new ExamNumber();
        /* 1、保存问卷表单信息 */
        // 获取问卷提交人的问卷提交记录

        //判断是否已过考试时间
        ExamSummary examSummaryByCode = examSummaryService.findExamSummaryByCode(o.getExamCode());
        LocalDateTime examEndTime = examSummaryByCode.getExamEndTime();

        LocalDateTime nowTime = LocalDateTime.now();
        boolean before = nowTime.isBefore(examEndTime);
        Assert.isTrue(before, "考试时间已过！");
        ExamInfo info = examInfoRepository.findAllByPublishUsername(o.getPublishUsername(), o.getExamAppCode());

        int getAnswers = 0;//答对个数
        String examRecord = o.getExamRecord();//题目
        String examAnswer = o.getExamAnswer();//答案
        String[] examRecords = examRecord.split(",");
        String[] examAnswers = examAnswer.split(",");
        for (int i = 0; i < examRecords.length; i++) {
            List<String> rightAnswerList = iExamQuestionAnswerService.findIsCorrectByExamCode(examRecords[i], "1");
            if (CollUtil.isNotEmpty(rightAnswerList)) {
                String rightAnswers = StringUtils.join(rightAnswerList, "/");
                if (examAnswers[i].equals(rightAnswers)) {//答案一样就正确
                    getAnswers += 1;
                }
            }
        }
        o.setScore(String.valueOf(getAnswers * 5));//答对的个数乘以每题的分数每题五分
        examNumber = iExamNumberService.findExamNumber(o.getExamCode(), o.getPublishUsername());
        if (ObjectUtils.isNotEmpty(examNumber)) {
            o.setExamNumber(examNumber.getExamNumber() + 1);
            int i = examNumber.getExamNumber() + 1;
            examNumber.setExamNumber(i);
            iExamNumberService.update(examNumber);
        } else {
            o.setExamNumber(1);
            ExamNumber examNumber1 = new ExamNumber();
            examNumber1.setExamCode(o.getExamCode());
            examNumber1.setExamNumber(1);
            examNumber1.setExamUserName(o.getPublishUsername());
            iExamNumberService.insert(examNumber1);
        }

        // 如果提交人未提交过，需要新增问卷提交记录信息
        if (info == null) {
            IUser currentUser = SecurityUtils.getCurrentUser();
            Set<? extends IOrg> authOrgs = currentUser.getAuthOrgs();
            Set<? extends IPosition> authPositions = currentUser.getAuthPositions();
            List<String> orgCode = Lists.newArrayList();
            List<String> orgName = Lists.newArrayList();
            List<String> positionName = Lists.newArrayList();
            for (IOrg iOrg : authOrgs) {
                orgCode.add(iOrg.getOrgCode());
                orgName.add(iOrg.getOrgName());
            }
            for (IPosition authPosition : authPositions) {
                positionName.add(authPosition.getPositionName());
            }
            o.setId(null);
            o.setPublishTruename(currentUser.getTruename());
            o.setPublishUsername(currentUser.getUsername());
            o.setDepartmentCode(StringUtils.join(orgCode, ","));
            o.setDepartmentName(StringUtils.join(orgName, ","));
            o.setPositionName(StringUtils.join(positionName, ","));
            o.setIsMarkingExam(false);
            o.setIsFinishExam(true);
            result = this.insert(o);
        } else {
            // 如果提交人提交过问卷，则将问卷提交记录状态置为已答卷
            o.setId(info.getId());
            o.setIsFinishExam(true);
            result = this.update(o);
        }
        if (o.getExamNumber() < 3 && Integer.parseInt(o.getScore()) < 90) {////不及格
            log.info("考试次数为【{}】，考试分数为【{}】", o.getExamNumber(), o.getScore());
            if (ObjectUtils.isNotEmpty(info)) {
                deleteById(info.getId());
            } else {
                delete(o);
            }
            return o;
        }


        /* 2、核销统一待办 */
        // 将考试编码转换为待办类型
        ExamSummary summaryInfo = examSummaryService.findExamSummaryByCode(result.getExamCode());
        Assert.notNull(summaryInfo, "考试信息不存在!");
        String workType = summaryInfo.getWorkType();
        Assert.isTrue(StringUtils.isNotEmpty(workType), "未指定考试的待办类型");
        // 根据提交人和待办类型获取待办工作并进行核销操作
        List<ExamWork> workInfo = examWorkService.findByUsernameAndWorkType(o.getPublishUsername(), workType);
        for (ExamWork examWork : workInfo) {
            if (null != examWork) {
                examWorkService.dealWith(source, currentUserCode, examWork.getId());
            } else {
                // 提交问卷操作时待办一定存在
                log.error("根据username【{}】、workType【{}】未能成功核销统一待办!", o.getPublishUsername(), workType);
            }
        }
        /* 3、返回入库数据 */
        return result;
    }

    /**
     * 查询指定人的答题情况
     *
     * @param ：答题人账号 publishUsername
     *               考试编码      examCode
     *               答题试卷编码  examAppCode
     * @return 返回答题信息
     */
    @Override
    public ExamInfo findExamInfo(String publishUsername, String examCode, String examAppCode) {

        List<ExamInfo> allNoPage = this.findAllNoPage(Specifications
                .<ExamInfo>and()
                .eq("publishUsername", publishUsername)
                .eq("examCode", examCode)
                .eq(StrUtil.isNotEmpty(examAppCode), "examAppCode", examAppCode)
                .eq("enabled", true)
                .build());

        if (CollUtil.isNotEmpty(allNoPage)) return allNoPage.get(0);

        // 提交表查不到去自动保存表查询
        ExamInfoSave newInfo = examInfoSaveService.findByNewInfo(publishUsername, examCode, examAppCode);
        if (Objects.isNull(newInfo)) return null;

        ExamInfo info = new ExamInfo();
        BeanUtils.copyProperties(newInfo, info);
        SimpleUser user = uumsSysUserinfoApi.findByUsername(newInfo.getCreator(), Constants.APP_CODE);
        info.setPublishUsername(user.getUsername());
        info.setPublishTruename(user.getTruename());
        info.setDepartmentCode(user.getBelongDepartmentCode());
        info.setDepartmentName(user.getBelongDepartmentName());
        info.setIsFinishExam(false);
        info.setIsMarkingExam(false);

        return info;
    }

    @Override
    public ExamInfo findExamInfo(String publishUsername, String examAppCode) {
        return this.findOne(Specifications.<ExamInfo>and()
                .eq("publishUsername", publishUsername)
                .eq("examAppCode", examAppCode)
                .build());
    }

    @Override
    public ExamInfo findExamInfo1(String publishUsername, String examAppCode) {

        long count = this.count(Specifications
                .<ExamInfo>and()
                .eq("publishUsername", publishUsername)
                .eq(StrUtil.isNotEmpty(examAppCode), "examAppCode", examAppCode)
                .eq("enabled", true)
                .build());

        if (count > 0) return null;

        // 去自动保存表查询
        ExamInfoSave newInfo = examInfoSaveRepository.findAllByNew1(publishUsername, examAppCode);
        if (Objects.isNull(newInfo)) return null;

        ExamInfo info = new ExamInfo();
        BeanUtils.copyProperties(newInfo, info, "id");
        SimpleUser user = uumsSysUserinfoApi.findByUsername(newInfo.getCreator(), Constants.APP_CODE);
        info.setPublishUsername(user.getUsername());
        info.setPublishTruename(user.getTruename());
        info.setDepartmentCode(user.getBelongDepartmentCode());
        info.setDepartmentName(user.getBelongDepartmentName());
        info.setIsFinishExam(false);
        info.setIsMarkingExam(false);

        return info;
    }

    /**
     * 统计参与考试人数
     *
     * @param examCode 考试编码
     * @return 返回参与考试人数
     */
    @Override
    public Integer countActualExamNum(String examCode) {
        return examInfoRepository.countActualExamNum(examCode);
    }

    @Override
    public List<ExamInfo> findExamInfo(String examCode) {
        return this.findAllNoPage(Specifications
                .<ExamInfo>and()
                .eq("examCode", examCode)
                .eq("enabled", true)
                .build());
    }

    /**
     * @desc 根据考试编码查询当前人的考试成绩
     * <AUTHOR>
     */
    @Override
    public ExamInfo findExamInfoByExamCode(String examCode, String creator) {

        String currentUserName = SecurityUtils.getCurrentUserName();
        if (creator != null) currentUserName = creator;
        Specification<ExamInfo> spec = Specifications.<ExamInfo>and()
                .eq("examCode", examCode)
                .eq("publishUsername", currentUserName)
                .eq("enabled", true)
                .build();
        return this.findOne(spec);
    }

    /**
     * @desc 根据考试编码查询参与考试的人员
     * <AUTHOR>
     */
    @Override
    public Page<ExamInfo> findExamUserInfoByExamCode(String examInfo, String publishTruename, int page, int size) {
        Specification<ExamInfo> spec = Specifications.<ExamInfo>and()
                .eq("examCode", examInfo)
                .eq("enabled", true)
                .like(publishTruename != null, "publishTruename", "%" + publishTruename + "%")
                .build();
        Pageable pageable = this.getPageable(page, size);
        return this.findAll(spec, pageable);
    }

    /**
     * @desc 根据答题记录表的主键id添加批阅的分数
     * <AUTHOR>
     */
    @Override
    public ExamInfo updateExamInfo(ExamInfo examInfo) {
        return this.update(examInfo);
    }

    /**
     * @desc 根据考试编码 人员名称进行模糊查询参与考试的人员
     * <AUTHOR>
     */
    @Override
    public Page<ExamInfo> findUserByExamCodeTrueName(ExamInfo examInfo, int page, int size) {
        Specification<ExamInfo> spec = Specifications.<ExamInfo>and()
                .eq("examCode", examInfo.getExamCode())
                .eq("enabled", true)
                .like("publishTruename", "%" + examInfo.getPublishTruename() + "%")
                .build();
        Pageable pageable = this.getPageable(page, size);
        return this.findAll(spec, pageable);
    }

    /**
     * 测评结果查询
     *
     * @return
     */
    @Override
    public JsonResponse findTestResultSelect(Map<String, Object> paramMap, int pageIndex, int pageSize) {
        Page<List<Map<String, Object>>> page = null;
        Map<String, String> map = Maps.newHashMap();

        String testUserName = (String) paramMap.get("testUserName");
        String testResult = (String) paramMap.get("testResult");
        String examNumber = (String) paramMap.get("examNumber");
        try {
            /**定义sql**/
            StringBuffer sql = new StringBuffer("  select * from (select t.publish_username as user_name, " +
                    "       t.publish_truename as truename, " +
                    "       t.department_name as departmentName, " +
                    "       COALESCE(TO_NUMBER( max(t.score)),0) as score, " +
                    "       COALESCE( max(t.exam_number),0) as exam_number " +
                    "from US_EXAM_INFO t " +
                    " where t.exam_code like 'ljfx%' " +
                    " group by t.publish_username, t.publish_truename, t.department_name " +
                    "union all " +
                    "select distinct w.transactor_code as user_name, " +
                    "                v.truename as truename, " +
                    "                MAX(v.departmentName) as departmentName, " +
                    "                 0 as score, " +
                    "                 0 as exam_number " +
                    "  from us_exam_work w, uums.v_user_org_position v " +
                    " where w.transactor_code = v.username " +
                    " and w.transactor_code not in (select distinct t.publish_username " +
                    "  from US_EXAM_INFO t " +
                    " where t.exam_code like 'ljfx%' ) " +
                    "   and w.exam_code = 'ljfx-01' and w.enabled =1 " +
                    "   group by w.transactor_code, v.truename ) s" +
                    "   where  s.score <=100 ");
            if (StringUtils.isNotEmpty(testUserName)) {
                sql.append(" and s.truename like concat(concat('%',:testUserName),'%')");
                map.put("testUserName", testUserName);
            }
            if (StringUtils.isNotEmpty(testResult)) {
                if (Constants.ONE.equals(testResult)) {
                    sql.append(" and s.score >=90 ");
                } else {
                    sql.append(" and s.score <90 ");
                }
            }
            if (StringUtils.isNotEmpty(examNumber)) {
                sql.append(" and s.exam_number =:examNumber ");
                map.put("examNumber", examNumber);
            }

            sql.append(" order by score desc  ");
            List<Map<String, Object>> list = customDynamicWhere.queryNamedParameterForList(sql.toString(), map);
            List<Map<String, Object>> result = new ArrayList<>();

            for (Map<String, Object> stringObjectMap : list) {
                Map<String, Object> resultMap = new HashMap<>();
                String scores = stringObjectMap.get("SCORE").toString();
                Integer score = Integer.parseInt(scores);
                resultMap.put("userName", stringObjectMap.get("USER_NAME"));
                resultMap.put("trueName", stringObjectMap.get("TRUENAME"));
                resultMap.put("departmentName", stringObjectMap.get("DEPARTMENTNAME"));
                resultMap.put("score", score);
                resultMap.put("examNumber", stringObjectMap.get("EXAM_NUMBER"));
                if (score < 90) {
                    resultMap.put("pass", Constants.NO_PASS);
                } else {
                    resultMap.put("pass", Constants.PASS);
                }
                result.add(resultMap);
            }

            /**构建page内容**/
            if (CollUtil.isNotEmpty(result)) {
                long size = result.size();
                Pageable pageable = getPageable(pageIndex, pageSize, null, null);
                List<Map<String, Object>> listPart = PageTool.pagination(result, pageIndex, pageSize);
                page = new PageImpl(listPart, pageable, size);
            }

        } catch (Exception e) {
            log.error("测评结果查询---------------------------------" + e.toString());
        }

        return JsonResponse.success(page);
    }

    /**
     * 测评结果导出
     *
     * @param request
     * @param response
     * @param paramMap
     * @return
     */
    @Override
    public void exportTestResultSelect(HttpServletRequest request, HttpServletResponse response, Map<String, Object> paramMap) {
        log.info("ExamInfoServiceImpl-->测评结果导出");
        try {
            //调用查询方法
            JsonResponse testResultSelect = findTestResultSelect(paramMap, 1, 10000);
            Page<Map<String, Object>> data = (Page<Map<String, Object>>) testResultSelect.getData();
            List<Map<String, Object>> content = data.getContent();
            List<ExamInfo> examInfos = new ArrayList<>();
            for (Map<String, Object> datum : content) {
                ExamInfo examInfo = new ExamInfo();
                examInfo.setExamNumber(Integer.parseInt(datum.get("examNumber").toString()));
                examInfo.setScore(datum.get("score").toString());
                examInfo.setPublishTruename(datum.get("trueName").toString());
                examInfo.setPass(datum.get("pass").toString());
                examInfo.setDepartmentName(datum.get("departmentName").toString());
                examInfos.add(examInfo);
            }

            //设置导出Excel的名称
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddhhmm");
            String dateStr = LocalDateTime.now().format(formatter);
            String fileName = "测评结果" + dateStr + ".xls";
//            String userAgent = request.getHeader("user-agent").toLowerCase();
//            if (userAgent.contains("msie") || userAgent.contains("like gecko")) {
//                // win10 ie edge 浏览器 和其他系统的ie
//                fileName
//            }
            File targetFile = new File(fileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<ExamInfo> exportUtil = new ExcelUtil<>(ExamInfo.class);
            exportUtil.exportExcel(examInfos, Constants.SHEET_NAME, new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (Exception e) {
            log.error("---------------测评结果导出报错------------------------" + e.toString());
        }
    }


}

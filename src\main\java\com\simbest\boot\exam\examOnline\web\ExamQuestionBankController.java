/*
 * 版权所有 © 北京晟壁科技有限公司 2008-2027。保留一切权利!
 */
package com.simbest.boot.exam.examOnline.web;/**
 * Created by KZH on 2019/10/8 15:17.
 */

import cn.hutool.core.map.MapUtil;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.exam.examOnline.model.ExamQuestionBank;
import com.simbest.boot.exam.examOnline.service.IExamQuestionBankService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-10-08 15:17
 * @desc 题库
 **/
@Api(description = "题库")
@Slf4j
@RestController
@RequestMapping(value = "/action/examQuestionBank")
public class ExamQuestionBankController extends LogicController<ExamQuestionBank, String> {

    private IExamQuestionBankService iExamQuestionBankService;

    @Autowired
    public ExamQuestionBankController(IExamQuestionBankService service) {
        super(service);
        this.iExamQuestionBankService = service;
    }


    @Override
    public JsonResponse findAllNoPage(ExamQuestionBank examQuestionBank) {
        Specification<ExamQuestionBank> specification = iExamQuestionBankService.getSpecification(examQuestionBank);
        return JsonResponse.success(iExamQuestionBankService.findAllNoPage( specification , Sort.by(Sort.Direction.DESC , "createdTime")));
    }

    @ApiOperation(value = "根据登陆用户查询相对应的题库", notes = "根据登陆用户查询相对应的题库")
    @PostMapping(value = {"/listExamQuestionBank", "/sso/listExamQuestionBank", "/api/listExamQuestionBank"})
    public JsonResponse listExamQuestionBank(@RequestParam(required = false, defaultValue = "1") int page,
                                             @RequestParam(required = false, defaultValue = "10") int size,
                                             @RequestParam(required = false) String direction,
                                             @RequestParam(required = false) String properties,
                                             @RequestBody(required = false) ExamQuestionBank examQuestionBank) {
                if (direction == null) {
                    direction = "desc";
                }
                if (properties == null) {
                    properties = "createdTime";
                }
        Pageable pageable = iExamQuestionBankService.getPageable(page, size, direction, properties);
        return JsonResponse.success(iExamQuestionBankService.listByUserName(pageable, examQuestionBank));

    }

    @ApiOperation(value = "根据登陆用户查询相对应的题库信息不分页", notes = "根据登陆用户查询相对于的题库信息不分页")
    @PostMapping(value = {"/listExamQuestionBankNoPage", "/sso/listExamQuestionBankNoPage", "/api/listExamQuestionBankNoPage"})
    public JsonResponse listExamQuestionBank() {
        return JsonResponse.success(iExamQuestionBankService.listByUserNameNoPage());
    }

    @ApiOperation(value = "创建题库", notes = "创建题库")
    @PostMapping(value = {"/createExamQuestionBank", "/sso/createExamQuestionBank", "/api/createExamQuestionBank"})
    public JsonResponse createExamQuestionBank(@RequestBody ExamQuestionBank examQuestionBank) {
        String bank = iExamQuestionBankService.creatExamQuestionBank(examQuestionBank);
        if (bank.contains("不可用")) return JsonResponse.fail(bank);
        return JsonResponse.success(bank);
    }

    @ApiOperation(value = "修改题库", notes = "修改题库")
    @PostMapping(value = {"/updateExamQuestionBank", "/sso/updateExamQuestionBank", "/api/updateExamQuestionBank"})
    public JsonResponse updateExamQuestionBank(@RequestBody ExamQuestionBank examQuestionBank) {
        String bank = iExamQuestionBankService.updateExamQuestionBank(examQuestionBank);
        if (bank.contains("不可用")) return JsonResponse.fail(bank);
        return JsonResponse.success(bank);
    }

    @ApiOperation(value = "删除题库并将题目删除", notes = "删除题库并将题目删除")
    @PostMapping(value = {"/delExamQuestionBank", "/sso/delExamQuestionBank", "/api/delExamQuestionBank"})
    public JsonResponse delExamQuestionBank(@RequestParam String id) {

        String bank = iExamQuestionBankService.delExamQuestionBank(id);
        return JsonResponse.success(bank);
    }


}
